package com.libretv.ui.player

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.MediaItem
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.hls.HlsMediaSource
import androidx.media3.datasource.DefaultHttpDataSource
import com.libretv.databinding.FragmentPlayerBinding
import kotlinx.coroutines.launch

@UnstableApi
class PlayerFragment : Fragment() {
    
    private var _binding: FragmentPlayerBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: PlayerViewModel by viewModels()
    private var exoPlayer: ExoPlayer? = null
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPlayerBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initializePlayer()
        observeViewModel()
        
        // 从参数获取视频URL
        arguments?.getString("videoUrl")?.let { url ->
            viewModel.loadVideo(url)
        }
    }
    
    private fun initializePlayer() {
        exoPlayer = ExoPlayer.Builder(requireContext()).build()
        binding.playerView.player = exoPlayer
    }
    
    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.processedVideoUrl.collect { url ->
                url?.let { playVideo(it) }
            }
        }
        
        lifecycleScope.launch {
            viewModel.isLoading.collect { isLoading ->
                binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            }
        }
    }
    
    private fun playVideo(url: String) {
        exoPlayer?.let { player ->
            // 创建数据源工厂
            val dataSourceFactory = DefaultHttpDataSource.Factory()
                .setUserAgent("LibreTV-Android/1.0")
                .setConnectTimeoutMs(30000)
                .setReadTimeoutMs(30000)
            
            // 创建HLS媒体源
            val hlsMediaSource = HlsMediaSource.Factory(dataSourceFactory)
                .createMediaSource(MediaItem.fromUri(url))
            
            player.setMediaSource(hlsMediaSource)
            player.prepare()
            player.playWhenReady = true
        }
    }
    
    override fun onPause() {
        super.onPause()
        exoPlayer?.pause()
    }
    
    override fun onResume() {
        super.onResume()
        exoPlayer?.play()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        exoPlayer?.release()
        exoPlayer = null
        _binding = null
    }
}

// PlayerViewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.libretv.data.network.VideoRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

class PlayerViewModel : ViewModel() {
    
    private val repository = VideoRepository()
    
    private val _processedVideoUrl = MutableStateFlow<String?>(null)
    val processedVideoUrl: StateFlow<String?> = _processedVideoUrl
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading
    
    fun loadVideo(originalUrl: String) {
        viewModelScope.launch {
            _isLoading.value = true
            
            // 通过后端API获取过滤后的M3U8
            repository.getFilteredM3U8(originalUrl)
                .onSuccess { filteredContent ->
                    // 这里可以进一步处理M3U8内容
                    // 或者直接使用后端代理URL
                    val proxyUrl = "https://your-api-domain.com/api/proxy/m3u8?url=${java.net.URLEncoder.encode(originalUrl, "UTF-8")}"
                    _processedVideoUrl.value = proxyUrl
                }
                .onFailure {
                    // 失败时直接使用原始URL
                    _processedVideoUrl.value = originalUrl
                }
            
            _isLoading.value = false
        }
    }
}
