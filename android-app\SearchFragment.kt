package com.libretv.ui.search

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import com.libretv.databinding.FragmentSearchBinding
import com.libretv.ui.adapter.VideoAdapter
import kotlinx.coroutines.launch

class SearchFragment : Fragment() {
    
    private var _binding: FragmentSearchBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: SearchViewModel by viewModels()
    private lateinit var videoAdapter: VideoAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSearchBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerView()
        setupSearchView()
        observeViewModel()
    }
    
    private fun setupRecyclerView() {
        videoAdapter = VideoAdapter { videoItem ->
            // 点击视频项，导航到详情页
            val action = SearchFragmentDirections.actionSearchToDetail(
                videoId = videoItem.id,
                source = videoItem.source
            )
            findNavController().navigate(action)
        }
        
        binding.recyclerView.apply {
            layoutManager = GridLayoutManager(context, 2)
            adapter = videoAdapter
        }
    }
    
    private fun setupSearchView() {
        binding.searchView.setOnQueryTextListener(object : androidx.appcompat.widget.SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                query?.let { 
                    if (it.isNotBlank()) {
                        viewModel.searchVideos(it)
                    }
                }
                return true
            }
            
            override fun onQueryTextChange(newText: String?): Boolean {
                return false
            }
        })
    }
    
    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.searchResults.collect { videos ->
                videoAdapter.submitList(videos)
            }
        }
        
        lifecycleScope.launch {
            viewModel.isLoading.collect { isLoading ->
                binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            }
        }
        
        lifecycleScope.launch {
            viewModel.error.collect { error ->
                error?.let {
                    Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}

// ViewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.libretv.data.api.VideoItem
import com.libretv.data.network.VideoRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

class SearchViewModel : ViewModel() {
    
    private val repository = VideoRepository()
    
    private val _searchResults = MutableStateFlow<List<VideoItem>>(emptyList())
    val searchResults: StateFlow<List<VideoItem>> = _searchResults
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error
    
    fun searchVideos(keyword: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            repository.searchVideos(keyword)
                .onSuccess { response ->
                    _searchResults.value = response.list
                }
                .onFailure { exception ->
                    _error.value = exception.message ?: "搜索失败"
                }
            
            _isLoading.value = false
        }
    }
}
