  Activity android.app  Greeting android.app.Activity  Modifier android.app.Activity  Scaffold android.app.Activity  ShipinTheme android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  onCreate android.app.Activity  padding android.app.Activity  
setContent android.app.Activity  Context android.content  Greeting android.content.Context  Modifier android.content.Context  Scaffold android.content.Context  ShipinTheme android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  padding android.content.Context  
setContent android.content.Context  Greeting android.content.ContextWrapper  Modifier android.content.ContextWrapper  Scaffold android.content.ContextWrapper  ShipinTheme android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  padding android.content.ContextWrapper  
setContent android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  Greeting  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Scaffold  android.view.ContextThemeWrapper  ShipinTheme  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  Greeting #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Scaffold #androidx.activity.ComponentActivity  ShipinTheme #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  Greeting -androidx.activity.ComponentActivity.Companion  Modifier -androidx.activity.ComponentActivity.Companion  Scaffold -androidx.activity.ComponentActivity.Companion  ShipinTheme -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  padding -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  isSystemInDarkTheme androidx.compose.foundation  
PaddingValues "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  ColorScheme androidx.compose.material3  
MaterialTheme androidx.compose.material3  Scaffold androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  Modifier androidx.compose.ui  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Preview #androidx.compose.ui.tooling.preview  TextUnit androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  Greeting #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Scaffold #androidx.core.app.ComponentActivity  ShipinTheme #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  Bundle com.example.shipin  ComponentActivity com.example.shipin  
Composable com.example.shipin  Greeting com.example.shipin  GreetingPreview com.example.shipin  MainActivity com.example.shipin  Modifier com.example.shipin  Preview com.example.shipin  Scaffold com.example.shipin  ShipinTheme com.example.shipin  String com.example.shipin  fillMaxSize com.example.shipin  padding com.example.shipin  Greeting com.example.shipin.MainActivity  Modifier com.example.shipin.MainActivity  Scaffold com.example.shipin.MainActivity  ShipinTheme com.example.shipin.MainActivity  enableEdgeToEdge com.example.shipin.MainActivity  fillMaxSize com.example.shipin.MainActivity  padding com.example.shipin.MainActivity  
setContent com.example.shipin.MainActivity  Boolean com.example.shipin.ui.theme  Build com.example.shipin.ui.theme  
Composable com.example.shipin.ui.theme  DarkColorScheme com.example.shipin.ui.theme  
FontFamily com.example.shipin.ui.theme  
FontWeight com.example.shipin.ui.theme  LightColorScheme com.example.shipin.ui.theme  Pink40 com.example.shipin.ui.theme  Pink80 com.example.shipin.ui.theme  Purple40 com.example.shipin.ui.theme  Purple80 com.example.shipin.ui.theme  PurpleGrey40 com.example.shipin.ui.theme  PurpleGrey80 com.example.shipin.ui.theme  ShipinTheme com.example.shipin.ui.theme  
Typography com.example.shipin.ui.theme  Unit com.example.shipin.ui.theme  sp 
kotlin.Double  	compareTo 
kotlin.Int                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     