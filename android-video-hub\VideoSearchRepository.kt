package com.videohub.data.repository

import com.videohub.data.api.*
import com.videohub.core.network.NetworkService
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 视频搜索仓库 - 实现LibreTV的多源聚合搜索逻辑
 */
@Singleton
class VideoSearchRepository @Inject constructor(
    private val networkService: NetworkService,
    private val contentFilter: ContentFilterService
) {
    
    /**
     * 多源聚合搜索（学习LibreTV的search函数逻辑）
     */
    suspend fun searchVideos(
        query: String,
        enabledSources: List<ApiSource>,
        enableContentFilter: Boolean = true
    ): Flow<SearchResult> = flow {
        
        emit(SearchResult.Loading)
        
        try {
            // 并发搜索所有启用的源（学习LibreTV的Promise.all逻辑）
            val searchResults = enabledSources.map { source ->
                async(Dispatchers.IO) {
                    searchSingleSource(query, source)
                }
            }.awaitAll()
            
            // 合并所有结果
            val allResults = searchResults.flatten()
            
            // 应用内容过滤（学习LibreTV的yellowFilterEnabled逻辑）
            val filteredResults = if (enableContentFilter) {
                contentFilter.filterAdultContent(allResults)
            } else {
                allResults
            }
            
            emit(SearchResult.Success(filteredResults))
            
        } catch (e: Exception) {
            emit(SearchResult.Error(e.message ?: "搜索失败"))
        }
    }
    
    /**
     * 单个源搜索实现
     */
    private suspend fun searchSingleSource(
        query: String, 
        source: ApiSource
    ): List<VideoItem> {
        return try {
            // 构建搜索URL
            val searchUrl = "${source.baseUrl}${ApiSourceConfig.RequestConfig.SEARCH_PATH}${query}"
            
            // 发起网络请求（带超时控制）
            val response = withTimeout(ApiSourceConfig.RequestConfig.TIMEOUT_SECONDS * 1000) {
                networkService.get<ApiResponse<VideoItem>>(searchUrl)
            }
            
            // 为每个结果添加源信息
            response.list.map { item ->
                item.copy(
                    source_name = source.name,
                    source_code = source.id
                )
            }
            
        } catch (e: TimeoutCancellationException) {
            // 超时处理
            emptyList()
        } catch (e: Exception) {
            // 其他错误处理
            emptyList()
        }
    }
    
    /**
     * 获取视频详情（学习LibreTV的showDetails逻辑）
     */
    suspend fun getVideoDetail(
        videoId: String,
        sourceCode: String
    ): Result<VideoDetail> {
        return try {
            val source = ApiSourceConfig.DEFAULT_SOURCES.find { it.id == sourceCode }
                ?: return Result.failure(Exception("未找到视频源"))
            
            val detailUrl = "${source.baseUrl}${ApiSourceConfig.RequestConfig.DETAIL_PATH}${videoId}"
            val response = networkService.get<ApiResponse<VideoItem>>(detailUrl)
            
            if (response.list.isEmpty()) {
                return Result.failure(Exception("视频不存在"))
            }
            
            val videoInfo = response.list.first()
            val episodes = parsePlayUrls(videoInfo.vod_play_url ?: "")
            
            Result.success(VideoDetail(videoInfo, episodes))
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 解析播放地址（学习LibreTV的播放地址解析逻辑）
     */
    private fun parsePlayUrls(playUrlString: String): List<Episode> {
        if (playUrlString.isBlank()) return emptyList()
        
        return try {
            val episodes = mutableListOf<Episode>()
            
            // 解析格式: 第01集$url1#第02集$url2
            val sources = playUrlString.split("$$$")
            if (sources.isNotEmpty()) {
                val mainSource = sources[0]
                val episodeList = mainSource.split("#")
                
                episodeList.forEachIndexed { index, episode ->
                    val parts = episode.split("$")
                    if (parts.size >= 2) {
                        episodes.add(
                            Episode(
                                index = index,
                                name = parts[0].ifBlank { "第${index + 1}集" },
                                url = parts[1]
                            )
                        )
                    }
                }
            }
            
            episodes
        } catch (e: Exception) {
            emptyList()
        }
    }
}

/**
 * 搜索结果密封类
 */
sealed class SearchResult {
    object Loading : SearchResult()
    data class Success(val videos: List<VideoItem>) : SearchResult()
    data class Error(val message: String) : SearchResult()
}

/**
 * 内容过滤服务（学习LibreTV的内容过滤逻辑）
 */
@Singleton
class ContentFilterService @Inject constructor() {
    
    /**
     * 过滤成人内容
     */
    fun filterAdultContent(videos: List<VideoItem>): List<VideoItem> {
        return videos.filter { video ->
            val typeName = video.type_name ?: ""
            val videoName = video.vod_name
            val content = "$typeName $videoName".lowercase()
            
            // 检查是否包含敏感关键词
            !ApiSourceConfig.ContentFilter.ADULT_KEYWORDS.any { keyword ->
                content.contains(keyword.lowercase())
            }
        }
    }
    
    /**
     * 按质量过滤
     */
    fun filterByQuality(videos: List<VideoItem>, preferHighQuality: Boolean = true): List<VideoItem> {
        if (!preferHighQuality) return videos
        
        return videos.sortedByDescending { video ->
            val content = "${video.vod_name} ${video.vod_remarks ?: ""}".lowercase()
            ApiSourceConfig.ContentFilter.QUALITY_KEYWORDS.count { keyword ->
                content.contains(keyword.lowercase())
            }
        }
    }
    
    /**
     * 去重处理（可选功能）
     */
    fun removeDuplicates(videos: List<VideoItem>): List<VideoItem> {
        val uniqueVideos = mutableListOf<VideoItem>()
        val seenNames = mutableSetOf<String>()
        
        videos.forEach { video ->
            val normalizedName = video.vod_name.replace(Regex("[\\s\\-_]+"), "").lowercase()
            if (!seenNames.contains(normalizedName)) {
                seenNames.add(normalizedName)
                uniqueVideos.add(video)
            }
        }
        
        return uniqueVideos
    }
}
