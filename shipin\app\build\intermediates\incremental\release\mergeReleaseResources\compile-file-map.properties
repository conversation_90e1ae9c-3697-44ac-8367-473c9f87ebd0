#Wed Jun 04 14:34:46 CST 2025
com.example.shipin.app-main-67\:/drawable/ic_launcher_background.xml=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_launcher_background.xml.flat
com.example.shipin.app-main-67\:/drawable/ic_launcher_foreground.xml=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_launcher_foreground.xml.flat
com.example.shipin.app-main-67\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.shipin.app-main-67\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.shipin.app-main-67\:/mipmap-hdpi/ic_launcher.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.shipin.app-main-67\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.shipin.app-main-67\:/mipmap-mdpi/ic_launcher.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.shipin.app-main-67\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.shipin.app-main-67\:/mipmap-xhdpi/ic_launcher.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.shipin.app-main-67\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.shipin.app-main-67\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.shipin.app-main-67\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.shipin.app-main-67\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.shipin.app-main-67\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.shipin.app-main-67\:/xml/backup_rules.xml=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_backup_rules.xml.flat
com.example.shipin.app-main-67\:/xml/data_extraction_rules.xml=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_data_extraction_rules.xml.flat
