package com.example.shipin.data.repository;

/**
 * 视频搜索仓库 - 实现LibreTV的多源聚合搜索逻辑
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J,\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u000bH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\r\u0010\u000eJ\u0016\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010\u0012\u001a\u00020\u000bH\u0002J$\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00140\u00102\u0006\u0010\u0015\u001a\u00020\u000b2\u0006\u0010\u0016\u001a\u00020\u0017H\u0082@\u00a2\u0006\u0002\u0010\u0018J4\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u001b0\u001a2\u0006\u0010\u0015\u001a\u00020\u000b2\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00170\u00102\b\b\u0002\u0010\u001d\u001a\u00020\u001eH\u0086@\u00a2\u0006\u0002\u0010\u001fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006 "}, d2 = {"Lcom/example/shipin/data/repository/VideoRepository;", "", "apiService", "Lcom/example/shipin/data/api/VideoApiService;", "contentFilter", "Lcom/example/shipin/data/repository/ContentFilterService;", "(Lcom/example/shipin/data/api/VideoApiService;Lcom/example/shipin/data/repository/ContentFilterService;)V", "getVideoDetail", "Lkotlin/Result;", "Lcom/example/shipin/data/api/VideoDetail;", "videoId", "", "sourceCode", "getVideoDetail-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "parsePlayUrls", "", "Lcom/example/shipin/data/api/Episode;", "playUrlString", "searchSingleSource", "Lcom/example/shipin/data/api/VideoItem;", "query", "source", "Lcom/example/shipin/data/api/ApiSource;", "(Ljava/lang/String;Lcom/example/shipin/data/api/ApiSource;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchVideos", "Lkotlinx/coroutines/flow/Flow;", "Lcom/example/shipin/data/repository/SearchResult;", "enabledSources", "enableContentFilter", "", "(Ljava/lang/String;Ljava/util/List;ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
public final class VideoRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.shipin.data.api.VideoApiService apiService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.shipin.data.repository.ContentFilterService contentFilter = null;
    
    @javax.inject.Inject()
    public VideoRepository(@org.jetbrains.annotations.NotNull()
    com.example.shipin.data.api.VideoApiService apiService, @org.jetbrains.annotations.NotNull()
    com.example.shipin.data.repository.ContentFilterService contentFilter) {
        super();
    }
    
    /**
     * 多源聚合搜索（学习LibreTV的search函数逻辑）
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object searchVideos(@org.jetbrains.annotations.NotNull()
    java.lang.String query, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.shipin.data.api.ApiSource> enabledSources, boolean enableContentFilter, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.example.shipin.data.repository.SearchResult>> $completion) {
        return null;
    }
    
    /**
     * 单个源搜索实现
     */
    private final java.lang.Object searchSingleSource(java.lang.String query, com.example.shipin.data.api.ApiSource source, kotlin.coroutines.Continuation<? super java.util.List<com.example.shipin.data.api.VideoItem>> $completion) {
        return null;
    }
    
    /**
     * 解析播放地址（学习LibreTV的播放地址解析逻辑）
     */
    private final java.util.List<com.example.shipin.data.api.Episode> parsePlayUrls(java.lang.String playUrlString) {
        return null;
    }
}