const express = require('express');
const axios = require('axios');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());

// 视频源配置（精简版）
const VIDEO_SOURCES = {
    'source1': {
        name: '资源站1',
        api: 'https://api.example1.com/api.php/provide/vod',
        priority: 1
    },
    'source2': {
        name: '资源站2', 
        api: 'https://api.example2.com/api.php/provide/vod',
        priority: 2
    }
};

// 1. 搜索接口
app.get('/api/search', async (req, res) => {
    try {
        const { keyword, source = 'source1' } = req.query;
        
        if (!keyword) {
            return res.status(400).json({ error: '缺少搜索关键词' });
        }

        const sourceConfig = VIDEO_SOURCES[source];
        if (!sourceConfig) {
            return res.status(400).json({ error: '无效的视频源' });
        }

        const searchUrl = `${sourceConfig.api}?ac=videolist&wd=${encodeURIComponent(keyword)}`;
        
        const response = await axios.get(searchUrl, {
            timeout: 10000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });

        const data = response.data;
        
        // 数据清洗和格式化
        const cleanedData = {
            code: 200,
            total: data.total || 0,
            list: (data.list || []).map(item => ({
                id: item.vod_id,
                name: item.vod_name,
                pic: item.vod_pic,
                type: item.type_name,
                year: item.vod_year,
                area: item.vod_area,
                remarks: item.vod_remarks,
                source: source
            }))
        };

        res.json(cleanedData);
    } catch (error) {
        console.error('搜索错误:', error.message);
        res.status(500).json({ error: '搜索失败' });
    }
});

// 2. 视频详情接口
app.get('/api/detail/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { source = 'source1' } = req.query;

        const sourceConfig = VIDEO_SOURCES[source];
        if (!sourceConfig) {
            return res.status(400).json({ error: '无效的视频源' });
        }

        const detailUrl = `${sourceConfig.api}?ac=videolist&ids=${id}`;
        
        const response = await axios.get(detailUrl, {
            timeout: 10000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });

        const data = response.data;
        
        if (!data.list || data.list.length === 0) {
            return res.status(404).json({ error: '视频不存在' });
        }

        const video = data.list[0];
        
        // 解析播放地址
        const episodes = parsePlayUrls(video.vod_play_url);

        const result = {
            code: 200,
            data: {
                id: video.vod_id,
                name: video.vod_name,
                pic: video.vod_pic,
                content: video.vod_content,
                director: video.vod_director,
                actor: video.vod_actor,
                year: video.vod_year,
                area: video.vod_area,
                episodes: episodes
            }
        };

        res.json(result);
    } catch (error) {
        console.error('获取详情错误:', error.message);
        res.status(500).json({ error: '获取详情失败' });
    }
});

// 3. M3U8代理接口（核心广告过滤）
app.get('/api/proxy/m3u8', async (req, res) => {
    try {
        const { url } = req.query;
        
        if (!url) {
            return res.status(400).json({ error: '缺少URL参数' });
        }

        // 验证URL安全性
        if (!isValidUrl(url)) {
            return res.status(400).json({ error: '无效的URL' });
        }

        const response = await axios.get(url, {
            timeout: 15000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': new URL(url).origin
            }
        });

        let m3u8Content = response.data;

        // 广告过滤处理
        m3u8Content = filterAdsFromM3U8(m3u8Content, url);

        res.setHeader('Content-Type', 'application/vnd.apple.mpegurl');
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.send(m3u8Content);

    } catch (error) {
        console.error('M3U8代理错误:', error.message);
        res.status(500).json({ error: 'M3U8获取失败' });
    }
});

// 4. 通用代理接口
app.get('/api/proxy', async (req, res) => {
    try {
        const { url } = req.query;
        
        if (!url || !isValidUrl(url)) {
            return res.status(400).json({ error: '无效的URL' });
        }

        const response = await axios.get(url, {
            responseType: 'stream',
            timeout: 30000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });

        // 转发响应头
        Object.keys(response.headers).forEach(key => {
            if (!['content-encoding', 'content-length'].includes(key.toLowerCase())) {
                res.setHeader(key, response.headers[key]);
            }
        });

        response.data.pipe(res);

    } catch (error) {
        console.error('代理错误:', error.message);
        res.status(500).json({ error: '代理请求失败' });
    }
});

// 工具函数
function parsePlayUrls(playUrlString) {
    if (!playUrlString) return [];
    
    try {
        // 解析播放地址格式: 第01集$url1#第02集$url2
        const episodes = [];
        const sources = playUrlString.split('$$$');
        
        if (sources.length > 0) {
            const mainSource = sources[0];
            const episodeList = mainSource.split('#');
            
            episodeList.forEach((episode, index) => {
                const parts = episode.split('$');
                if (parts.length >= 2) {
                    episodes.push({
                        index: index,
                        name: parts[0] || `第${index + 1}集`,
                        url: parts[1]
                    });
                }
            });
        }
        
        return episodes;
    } catch (error) {
        console.error('解析播放地址失败:', error);
        return [];
    }
}

function filterAdsFromM3U8(content, baseUrl) {
    if (!content) return '';
    
    const lines = content.split('\n');
    const filteredLines = [];
    const baseUrlObj = new URL(baseUrl);
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        
        // 跳过空行
        if (!line) {
            filteredLines.push(line);
            continue;
        }
        
        // 过滤广告标记
        if (line.includes('#EXT-X-DISCONTINUITY')) {
            continue; // 跳过广告分割标记
        }
        
        // 处理相对URL
        if (!line.startsWith('#') && line.includes('.ts')) {
            if (!line.startsWith('http')) {
                // 转换为绝对URL
                const absoluteUrl = new URL(line, baseUrl).toString();
                filteredLines.push(`/api/proxy?url=${encodeURIComponent(absoluteUrl)}`);
            } else {
                filteredLines.push(`/api/proxy?url=${encodeURIComponent(line)}`);
            }
        } else {
            filteredLines.push(line);
        }
    }
    
    return filteredLines.join('\n');
}

function isValidUrl(string) {
    try {
        const url = new URL(string);
        return ['http:', 'https:'].includes(url.protocol);
    } catch {
        return false;
    }
}

// 启动服务器
app.listen(PORT, () => {
    console.log(`服务器运行在端口 ${PORT}`);
});

module.exports = app;
