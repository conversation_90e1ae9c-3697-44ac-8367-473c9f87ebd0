package com.example.shipin.core.player

import javax.inject.Inject
import javax.inject.Singleton

/**
 * M3U8广告过滤器 - 学习LibreTV的filterAdsFromM3U8逻辑
 */
@Singleton
class M3U8AdFilter @Inject constructor() {
    
    /**
     * 过滤M3U8内容中的广告（学习LibreTV的核心过滤算法）
     */
    fun filterAds(m3u8Content: String, strictMode: Boolean = false): String {
        if (m3u8Content.isBlank()) return ""
        
        val lines = m3u8Content.split("\n")
        val filteredLines = mutableListOf<String>()
        
        var i = 0
        while (i < lines.size) {
            val line = lines[i].trim()
            
            // 1. 过滤DISCONTINUITY标记（LibreTV的主要过滤逻辑）
            if (line.contains("#EXT-X-DISCONTINUITY")) {
                // 跳过DISCONTINUITY标记
                i++
                continue
            }
            
            // 2. 严格模式下的额外过滤
            if (strictMode) {
                // 过滤异常短的片段（可能是广告）
                if (line.startsWith("#EXTINF:")) {
                    val duration = extractDuration(line)
                    if (duration in 0.1..2.0) {
                        // 跳过当前EXTINF行和下一行的URL
                        i += 2
                        continue
                    }
                }
                
                // 过滤包含广告关键词的URL
                if (!line.startsWith("#") && containsAdKeywords(line)) {
                    i++
                    continue
                }
            }
            
            filteredLines.add(line)
            i++
        }
        
        return filteredLines.joinToString("\n")
    }
    
    /**
     * 提取EXTINF行中的时长
     */
    private fun extractDuration(extinfLine: String): Double {
        return try {
            val regex = Regex("#EXTINF:([\\d.]+),")
            val matchResult = regex.find(extinfLine)
            matchResult?.groupValues?.get(1)?.toDouble() ?: 0.0
        } catch (e: Exception) {
            0.0
        }
    }
    
    /**
     * 检查URL是否包含广告关键词
     */
    private fun containsAdKeywords(url: String): Boolean {
        val adKeywords = listOf("ad", "ads", "advertisement", "advert", "commercial")
        val lowerUrl = url.lowercase()
        return adKeywords.any { keyword -> lowerUrl.contains(keyword) }
    }
    
    /**
     * 处理主播放列表（Master Playlist）
     */
    fun processMasterPlaylist(content: String, baseUrl: String): String {
        val lines = content.split("\n")
        val processedLines = mutableListOf<String>()
        
        lines.forEach { line ->
            when {
                line.startsWith("#EXT-X-STREAM-INF") -> {
                    processedLines.add(line)
                }
                line.startsWith("http") || (!line.startsWith("#") && line.isNotBlank()) -> {
                    // 将相对URL转换为绝对URL
                    val absoluteUrl = resolveUrl(baseUrl, line)
                    processedLines.add(absoluteUrl)
                }
                else -> {
                    processedLines.add(line)
                }
            }
        }
        
        return processedLines.joinToString("\n")
    }
    
    /**
     * 解析相对URL为绝对URL
     */
    private fun resolveUrl(baseUrl: String, relativeUrl: String): String {
        return if (relativeUrl.startsWith("http")) {
            relativeUrl
        } else {
            val base = if (baseUrl.endsWith("/")) baseUrl.dropLast(1) else baseUrl
            val relative = if (relativeUrl.startsWith("/")) relativeUrl else "/$relativeUrl"
            "$base$relative"
        }
    }
}
