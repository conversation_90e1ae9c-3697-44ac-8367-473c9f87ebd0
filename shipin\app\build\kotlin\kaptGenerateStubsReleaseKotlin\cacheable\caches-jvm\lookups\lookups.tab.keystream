  Activity android.app  Application android.app  Bundle android.app.Activity  Bundle android.content.Context  Bundle android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  Bundle  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  isSystemInDarkTheme androidx.compose.foundation  
Composable "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  KeyboardActions  androidx.compose.foundation.text  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  
Composable &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
MaterialTheme androidx.compose.material3  
Typography androidx.compose.material3  com androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  com androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Color androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  ContentScale androidx.compose.ui.layout  LocalContext androidx.compose.ui.platform  LocalSoftwareKeyboardController androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  	ImeAction androidx.compose.ui.text.input  TextOverflow androidx.compose.ui.text.style  Preview #androidx.compose.ui.tooling.preview  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  
hiltViewModel  androidx.hilt.navigation.compose  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  
DetailUiState androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  
SearchUiState androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  VideoRepository androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  collectAsStateWithLifecycle androidx.lifecycle.compose  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  
AsyncImage coil.compose  ExperimentalMaterial3Api com.example.shipin  MainActivity com.example.shipin  OptIn com.example.shipin  	ShipinApp com.example.shipin  ShipinAppPreview com.example.shipin  ShipinApplication com.example.shipin  Bundle com.example.shipin.MainActivity  Boolean com.example.shipin.core.player  Double com.example.shipin.core.player  M3U8AdFilter com.example.shipin.core.player  String com.example.shipin.core.player  Boolean +com.example.shipin.core.player.M3U8AdFilter  Double +com.example.shipin.core.player.M3U8AdFilter  Inject +com.example.shipin.core.player.M3U8AdFilter  String +com.example.shipin.core.player.M3U8AdFilter  	ApiConfig com.example.shipin.data.api  ApiResponse com.example.shipin.data.api  	ApiSource com.example.shipin.data.api  Boolean com.example.shipin.data.api  Episode com.example.shipin.data.api  Flow com.example.shipin.data.api  Int com.example.shipin.data.api  List com.example.shipin.data.api  Result com.example.shipin.data.api  String com.example.shipin.data.api  VideoApiService com.example.shipin.data.api  VideoDetail com.example.shipin.data.api  	VideoItem com.example.shipin.data.api  listOf com.example.shipin.data.api  mapOf com.example.shipin.data.api  to com.example.shipin.data.api  	ApiSource %com.example.shipin.data.api.ApiConfig  	getLISTOf %com.example.shipin.data.api.ApiConfig  	getListOf %com.example.shipin.data.api.ApiConfig  getMAPOf %com.example.shipin.data.api.ApiConfig  getMapOf %com.example.shipin.data.api.ApiConfig  getTO %com.example.shipin.data.api.ApiConfig  getTo %com.example.shipin.data.api.ApiConfig  listOf %com.example.shipin.data.api.ApiConfig  mapOf %com.example.shipin.data.api.ApiConfig  to %com.example.shipin.data.api.ApiConfig  	getLISTOf 3com.example.shipin.data.api.ApiConfig.ContentFilter  	getListOf 3com.example.shipin.data.api.ApiConfig.ContentFilter  listOf 3com.example.shipin.data.api.ApiConfig.ContentFilter  getMAPOf 3com.example.shipin.data.api.ApiConfig.RequestConfig  getMapOf 3com.example.shipin.data.api.ApiConfig.RequestConfig  getTO 3com.example.shipin.data.api.ApiConfig.RequestConfig  getTo 3com.example.shipin.data.api.ApiConfig.RequestConfig  mapOf 3com.example.shipin.data.api.ApiConfig.RequestConfig  to 3com.example.shipin.data.api.ApiConfig.RequestConfig  Int 'com.example.shipin.data.api.ApiResponse  List 'com.example.shipin.data.api.ApiResponse  String 'com.example.shipin.data.api.ApiResponse  Boolean %com.example.shipin.data.api.ApiSource  Int %com.example.shipin.data.api.ApiSource  String %com.example.shipin.data.api.ApiSource  Int #com.example.shipin.data.api.Episode  String #com.example.shipin.data.api.Episode  ApiResponse +com.example.shipin.data.api.VideoApiService  GET +com.example.shipin.data.api.VideoApiService  Response +com.example.shipin.data.api.VideoApiService  String +com.example.shipin.data.api.VideoApiService  Url +com.example.shipin.data.api.VideoApiService  	VideoItem +com.example.shipin.data.api.VideoApiService  Episode 'com.example.shipin.data.api.VideoDetail  List 'com.example.shipin.data.api.VideoDetail  	VideoItem 'com.example.shipin.data.api.VideoDetail  String %com.example.shipin.data.api.VideoItem  	ApiSource "com.example.shipin.data.repository  Boolean "com.example.shipin.data.repository  ContentFilterService "com.example.shipin.data.repository  Episode "com.example.shipin.data.repository  Flow "com.example.shipin.data.repository  List "com.example.shipin.data.repository  Result "com.example.shipin.data.repository  SearchResult "com.example.shipin.data.repository  String "com.example.shipin.data.repository  VideoApiService "com.example.shipin.data.repository  VideoDetail "com.example.shipin.data.repository  	VideoItem "com.example.shipin.data.repository  VideoRepository "com.example.shipin.data.repository  Inject 7com.example.shipin.data.repository.ContentFilterService  List 7com.example.shipin.data.repository.ContentFilterService  	VideoItem 7com.example.shipin.data.repository.ContentFilterService  List /com.example.shipin.data.repository.SearchResult  SearchResult /com.example.shipin.data.repository.SearchResult  String /com.example.shipin.data.repository.SearchResult  	VideoItem /com.example.shipin.data.repository.SearchResult  String 5com.example.shipin.data.repository.SearchResult.Error  List 7com.example.shipin.data.repository.SearchResult.Success  	VideoItem 7com.example.shipin.data.repository.SearchResult.Success  	ApiSource 2com.example.shipin.data.repository.VideoRepository  Boolean 2com.example.shipin.data.repository.VideoRepository  ContentFilterService 2com.example.shipin.data.repository.VideoRepository  Episode 2com.example.shipin.data.repository.VideoRepository  Flow 2com.example.shipin.data.repository.VideoRepository  Inject 2com.example.shipin.data.repository.VideoRepository  List 2com.example.shipin.data.repository.VideoRepository  Result 2com.example.shipin.data.repository.VideoRepository  SearchResult 2com.example.shipin.data.repository.VideoRepository  String 2com.example.shipin.data.repository.VideoRepository  VideoApiService 2com.example.shipin.data.repository.VideoRepository  VideoDetail 2com.example.shipin.data.repository.VideoRepository  	VideoItem 2com.example.shipin.data.repository.VideoRepository  
NetworkModule com.example.shipin.di  SingletonComponent com.example.shipin.di  HttpLoggingInterceptor #com.example.shipin.di.NetworkModule  OkHttpClient #com.example.shipin.di.NetworkModule  Provides #com.example.shipin.di.NetworkModule  Retrofit #com.example.shipin.di.NetworkModule  	Singleton #com.example.shipin.di.NetworkModule  VideoApiService #com.example.shipin.di.NetworkModule  Boolean &com.example.shipin.presentation.detail  
Composable &com.example.shipin.presentation.detail  DetailScreen &com.example.shipin.presentation.detail  
DetailUiState &com.example.shipin.presentation.detail  DetailViewModel &com.example.shipin.presentation.detail  EpisodeChip &com.example.shipin.presentation.detail  EpisodesSection &com.example.shipin.presentation.detail  ErrorMessage &com.example.shipin.presentation.detail  ExperimentalMaterial3Api &com.example.shipin.presentation.detail  List &com.example.shipin.presentation.detail  MutableStateFlow &com.example.shipin.presentation.detail  OptIn &com.example.shipin.presentation.detail  	StateFlow &com.example.shipin.presentation.detail  String &com.example.shipin.presentation.detail  Unit &com.example.shipin.presentation.detail  VideoDescriptionSection &com.example.shipin.presentation.detail  VideoDetailContent &com.example.shipin.presentation.detail  VideoInfoSection &com.example.shipin.presentation.detail  asStateFlow &com.example.shipin.presentation.detail  com &com.example.shipin.presentation.detail  Boolean 4com.example.shipin.presentation.detail.DetailUiState  String 4com.example.shipin.presentation.detail.DetailUiState  VideoDetail 4com.example.shipin.presentation.detail.DetailUiState  
DetailUiState 6com.example.shipin.presentation.detail.DetailViewModel  Inject 6com.example.shipin.presentation.detail.DetailViewModel  MutableStateFlow 6com.example.shipin.presentation.detail.DetailViewModel  	StateFlow 6com.example.shipin.presentation.detail.DetailViewModel  String 6com.example.shipin.presentation.detail.DetailViewModel  VideoRepository 6com.example.shipin.presentation.detail.DetailViewModel  _uiState 6com.example.shipin.presentation.detail.DetailViewModel  asStateFlow 6com.example.shipin.presentation.detail.DetailViewModel  getASStateFlow 6com.example.shipin.presentation.detail.DetailViewModel  getAsStateFlow 6com.example.shipin.presentation.detail.DetailViewModel  Boolean &com.example.shipin.presentation.search  
Composable &com.example.shipin.presentation.search  ErrorMessage &com.example.shipin.presentation.search  ExperimentalMaterial3Api &com.example.shipin.presentation.search  List &com.example.shipin.presentation.search  MutableStateFlow &com.example.shipin.presentation.search  OptIn &com.example.shipin.presentation.search  	SearchBar &com.example.shipin.presentation.search  SearchHistorySection &com.example.shipin.presentation.search  
SearchResults &com.example.shipin.presentation.search  SearchScreen &com.example.shipin.presentation.search  
SearchUiState &com.example.shipin.presentation.search  SearchViewModel &com.example.shipin.presentation.search  	StateFlow &com.example.shipin.presentation.search  String &com.example.shipin.presentation.search  Unit &com.example.shipin.presentation.search  	VideoCard &com.example.shipin.presentation.search  asStateFlow &com.example.shipin.presentation.search  	emptyList &com.example.shipin.presentation.search  Boolean 4com.example.shipin.presentation.search.SearchUiState  List 4com.example.shipin.presentation.search.SearchUiState  String 4com.example.shipin.presentation.search.SearchUiState  	VideoItem 4com.example.shipin.presentation.search.SearchUiState  Inject 6com.example.shipin.presentation.search.SearchViewModel  List 6com.example.shipin.presentation.search.SearchViewModel  MutableStateFlow 6com.example.shipin.presentation.search.SearchViewModel  
SearchUiState 6com.example.shipin.presentation.search.SearchViewModel  	StateFlow 6com.example.shipin.presentation.search.SearchViewModel  String 6com.example.shipin.presentation.search.SearchViewModel  VideoRepository 6com.example.shipin.presentation.search.SearchViewModel  _searchHistory 6com.example.shipin.presentation.search.SearchViewModel  _uiState 6com.example.shipin.presentation.search.SearchViewModel  asStateFlow 6com.example.shipin.presentation.search.SearchViewModel  	emptyList 6com.example.shipin.presentation.search.SearchViewModel  getASStateFlow 6com.example.shipin.presentation.search.SearchViewModel  getAsStateFlow 6com.example.shipin.presentation.search.SearchViewModel  getEMPTYList 6com.example.shipin.presentation.search.SearchViewModel  getEmptyList 6com.example.shipin.presentation.search.SearchViewModel  Boolean com.example.shipin.ui.theme  DarkColorScheme com.example.shipin.ui.theme  LightColorScheme com.example.shipin.ui.theme  Pink40 com.example.shipin.ui.theme  Pink80 com.example.shipin.ui.theme  Purple40 com.example.shipin.ui.theme  Purple80 com.example.shipin.ui.theme  PurpleGrey40 com.example.shipin.ui.theme  PurpleGrey80 com.example.shipin.ui.theme  ShipinTheme com.example.shipin.ui.theme  
Typography com.example.shipin.ui.theme  Unit com.example.shipin.ui.theme  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  SingletonComponent dagger.hilt.components  	ApiSource 	java.lang  
DetailUiState 	java.lang  ExperimentalMaterial3Api 	java.lang  MutableStateFlow 	java.lang  
SearchUiState 	java.lang  SingletonComponent 	java.lang  asStateFlow 	java.lang  com 	java.lang  	emptyList 	java.lang  listOf 	java.lang  mapOf 	java.lang  to 	java.lang  TimeUnit java.util.concurrent  Inject javax.inject  	Singleton javax.inject  	ApiSource kotlin  Boolean kotlin  
DetailUiState kotlin  Double kotlin  ExperimentalMaterial3Api kotlin  Int kotlin  Long kotlin  MutableStateFlow kotlin  OptIn kotlin  Pair kotlin  Result kotlin  
SearchUiState kotlin  SingletonComponent kotlin  String kotlin  Unit kotlin  asStateFlow kotlin  com kotlin  	emptyList kotlin  listOf kotlin  mapOf kotlin  to kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getSP 
kotlin.Int  getSp 
kotlin.Int  getTO 
kotlin.String  getTo 
kotlin.String  	ApiSource kotlin.annotation  
DetailUiState kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  MutableStateFlow kotlin.annotation  Result kotlin.annotation  
SearchUiState kotlin.annotation  SingletonComponent kotlin.annotation  asStateFlow kotlin.annotation  com kotlin.annotation  	emptyList kotlin.annotation  listOf kotlin.annotation  mapOf kotlin.annotation  to kotlin.annotation  	ApiSource kotlin.collections  
DetailUiState kotlin.collections  ExperimentalMaterial3Api kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableStateFlow kotlin.collections  Result kotlin.collections  
SearchUiState kotlin.collections  SingletonComponent kotlin.collections  asStateFlow kotlin.collections  com kotlin.collections  	emptyList kotlin.collections  listOf kotlin.collections  mapOf kotlin.collections  to kotlin.collections  	ApiSource kotlin.comparisons  
DetailUiState kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  MutableStateFlow kotlin.comparisons  Result kotlin.comparisons  
SearchUiState kotlin.comparisons  SingletonComponent kotlin.comparisons  asStateFlow kotlin.comparisons  com kotlin.comparisons  	emptyList kotlin.comparisons  listOf kotlin.comparisons  mapOf kotlin.comparisons  to kotlin.comparisons  	ApiSource 	kotlin.io  
DetailUiState 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  MutableStateFlow 	kotlin.io  Result 	kotlin.io  
SearchUiState 	kotlin.io  SingletonComponent 	kotlin.io  asStateFlow 	kotlin.io  com 	kotlin.io  	emptyList 	kotlin.io  listOf 	kotlin.io  mapOf 	kotlin.io  to 	kotlin.io  	ApiSource 
kotlin.jvm  
DetailUiState 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  Result 
kotlin.jvm  
SearchUiState 
kotlin.jvm  SingletonComponent 
kotlin.jvm  asStateFlow 
kotlin.jvm  com 
kotlin.jvm  	emptyList 
kotlin.jvm  listOf 
kotlin.jvm  mapOf 
kotlin.jvm  to 
kotlin.jvm  	ApiSource 
kotlin.ranges  
DetailUiState 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  Result 
kotlin.ranges  
SearchUiState 
kotlin.ranges  SingletonComponent 
kotlin.ranges  asStateFlow 
kotlin.ranges  com 
kotlin.ranges  	emptyList 
kotlin.ranges  listOf 
kotlin.ranges  mapOf 
kotlin.ranges  to 
kotlin.ranges  KClass kotlin.reflect  	ApiSource kotlin.sequences  
DetailUiState kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  MutableStateFlow kotlin.sequences  Result kotlin.sequences  
SearchUiState kotlin.sequences  SingletonComponent kotlin.sequences  asStateFlow kotlin.sequences  com kotlin.sequences  	emptyList kotlin.sequences  listOf kotlin.sequences  mapOf kotlin.sequences  to kotlin.sequences  	ApiSource kotlin.text  
DetailUiState kotlin.text  ExperimentalMaterial3Api kotlin.text  MutableStateFlow kotlin.text  Result kotlin.text  
SearchUiState kotlin.text  SingletonComponent kotlin.text  asStateFlow kotlin.text  com kotlin.text  	emptyList kotlin.text  listOf kotlin.text  mapOf kotlin.text  to kotlin.text  	ApiSource kotlinx.coroutines  Episode kotlinx.coroutines  Flow kotlinx.coroutines  Result kotlinx.coroutines  VideoApiService kotlinx.coroutines  VideoDetail kotlinx.coroutines  	VideoItem kotlinx.coroutines  launch kotlinx.coroutines  	ApiSource kotlinx.coroutines.flow  
DetailUiState kotlinx.coroutines.flow  Episode kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  Result kotlinx.coroutines.flow  
SearchUiState kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  VideoApiService kotlinx.coroutines.flow  VideoDetail kotlinx.coroutines.flow  	VideoItem kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  OkHttpClient okhttp3  HttpLoggingInterceptor okhttp3.logging  Response 	retrofit2  Retrofit 	retrofit2  GsonConverterFactory retrofit2.converter.gson  GET retrofit2.http  Url retrofit2.http                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  