package com.libretv.data.api

import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query

interface ApiService {
    
    @GET("api/search")
    suspend fun searchVideos(
        @Query("keyword") keyword: String,
        @Query("source") source: String = "source1"
    ): Response<SearchResponse>
    
    @GET("api/detail/{id}")
    suspend fun getVideoDetail(
        @Path("id") id: String,
        @Query("source") source: String = "source1"
    ): Response<DetailResponse>
    
    @GET("api/proxy/m3u8")
    suspend fun getM3U8(
        @Query("url") url: String
    ): Response<String>
}

// 数据模型
data class SearchResponse(
    val code: Int,
    val total: Int,
    val list: List<VideoItem>
)

data class VideoItem(
    val id: String,
    val name: String,
    val pic: String,
    val type: String?,
    val year: String?,
    val area: String?,
    val remarks: String?,
    val source: String
)

data class DetailResponse(
    val code: Int,
    val data: VideoDetail
)

data class VideoDetail(
    val id: String,
    val name: String,
    val pic: String,
    val content: String?,
    val director: String?,
    val actor: String?,
    val year: String?,
    val area: String?,
    val episodes: List<Episode>
)

data class Episode(
    val index: Int,
    val name: String,
    val url: String
)
