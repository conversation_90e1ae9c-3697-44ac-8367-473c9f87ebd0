int anim fragment_fast_out_extra_slow_in 0x7f010000
int animator fragment_close_enter 0x7f020000
int animator fragment_close_exit 0x7f020001
int animator fragment_fade_enter 0x7f020002
int animator fragment_fade_exit 0x7f020003
int animator fragment_open_enter 0x7f020004
int animator fragment_open_exit 0x7f020005
int array exo_controls_playback_speeds 0x7f030000
int attr action 0x7f040000
int attr ad_marker_color 0x7f040001
int attr ad_marker_width 0x7f040002
int attr alpha 0x7f040003
int attr animation_enabled 0x7f040004
int attr argType 0x7f040005
int attr artwork_display_mode 0x7f040006
int attr auto_show 0x7f040007
int attr backgroundTint 0x7f040008
int attr bar_gravity 0x7f040009
int attr bar_height 0x7f04000a
int attr buffered_color 0x7f04000b
int attr controller_layout_id 0x7f04000c
int attr data 0x7f04000d
int attr dataPattern 0x7f04000e
int attr default_artwork 0x7f04000f
int attr destination 0x7f040010
int attr enterAnim 0x7f040011
int attr exitAnim 0x7f040012
int attr fastScrollEnabled 0x7f040013
int attr fastScrollHorizontalThumbDrawable 0x7f040014
int attr fastScrollHorizontalTrackDrawable 0x7f040015
int attr fastScrollVerticalThumbDrawable 0x7f040016
int attr fastScrollVerticalTrackDrawable 0x7f040017
int attr font 0x7f040018
int attr fontProviderAuthority 0x7f040019
int attr fontProviderCerts 0x7f04001a
int attr fontProviderFallbackQuery 0x7f04001b
int attr fontProviderFetchStrategy 0x7f04001c
int attr fontProviderFetchTimeout 0x7f04001d
int attr fontProviderPackage 0x7f04001e
int attr fontProviderQuery 0x7f04001f
int attr fontProviderSystemFontFamily 0x7f040020
int attr fontStyle 0x7f040021
int attr fontVariationSettings 0x7f040022
int attr fontWeight 0x7f040023
int attr graph 0x7f040024
int attr hide_during_ads 0x7f040025
int attr hide_on_touch 0x7f040026
int attr keep_content_on_player_reset 0x7f040027
int attr lStar 0x7f040028
int attr launchSingleTop 0x7f040029
int attr layoutManager 0x7f04002a
int attr mimeType 0x7f04002b
int attr navGraph 0x7f04002c
int attr nestedScrollViewStyle 0x7f04002d
int attr nullable 0x7f04002e
int attr played_ad_marker_color 0x7f04002f
int attr played_color 0x7f040030
int attr player_layout_id 0x7f040031
int attr popEnterAnim 0x7f040032
int attr popExitAnim 0x7f040033
int attr popUpTo 0x7f040034
int attr popUpToInclusive 0x7f040035
int attr popUpToSaveState 0x7f040036
int attr queryPatterns 0x7f040037
int attr recyclerViewStyle 0x7f040038
int attr repeat_toggle_modes 0x7f040039
int attr resize_mode 0x7f04003a
int attr restoreState 0x7f04003b
int attr reverseLayout 0x7f04003c
int attr route 0x7f04003d
int attr scrubber_color 0x7f04003e
int attr scrubber_disabled_size 0x7f04003f
int attr scrubber_dragged_size 0x7f040040
int attr scrubber_drawable 0x7f040041
int attr scrubber_enabled_size 0x7f040042
int attr shortcutMatchRequired 0x7f040043
int attr show_buffering 0x7f040044
int attr show_fastforward_button 0x7f040045
int attr show_next_button 0x7f040046
int attr show_previous_button 0x7f040047
int attr show_rewind_button 0x7f040048
int attr show_shuffle_button 0x7f040049
int attr show_subtitle_button 0x7f04004a
int attr show_timeout 0x7f04004b
int attr show_vr_button 0x7f04004c
int attr shutter_background_color 0x7f04004d
int attr spanCount 0x7f04004e
int attr stackFromEnd 0x7f04004f
int attr startDestination 0x7f040050
int attr surface_type 0x7f040051
int attr targetPackage 0x7f040052
int attr time_bar_min_update_interval 0x7f040053
int attr touch_target_height 0x7f040054
int attr ttcIndex 0x7f040055
int attr unplayed_color 0x7f040056
int attr uri 0x7f040057
int attr use_artwork 0x7f040058
int attr use_controller 0x7f040059
int color androidx_core_ripple_material_light 0x7f050000
int color androidx_core_secondary_text_default_material_light 0x7f050001
int color black 0x7f050002
int color call_notification_answer_color 0x7f050003
int color call_notification_decline_color 0x7f050004
int color exo_black_opacity_60 0x7f050005
int color exo_black_opacity_70 0x7f050006
int color exo_bottom_bar_background 0x7f050007
int color exo_edit_mode_background_color 0x7f050008
int color exo_error_message_background_color 0x7f050009
int color exo_styled_error_message_background 0x7f05000a
int color exo_white 0x7f05000b
int color exo_white_opacity_70 0x7f05000c
int color notification_action_color_filter 0x7f05000d
int color notification_icon_bg_color 0x7f05000e
int color notification_material_background_media_default_color 0x7f05000f
int color primary_text_default_material_dark 0x7f050010
int color purple_200 0x7f050011
int color purple_500 0x7f050012
int color purple_700 0x7f050013
int color secondary_text_default_material_dark 0x7f050014
int color teal_200 0x7f050015
int color teal_700 0x7f050016
int color vector_tint_color 0x7f050017
int color vector_tint_theme_color 0x7f050018
int color white 0x7f050019
int dimen compat_button_inset_horizontal_material 0x7f060000
int dimen compat_button_inset_vertical_material 0x7f060001
int dimen compat_button_padding_horizontal_material 0x7f060002
int dimen compat_button_padding_vertical_material 0x7f060003
int dimen compat_control_corner_material 0x7f060004
int dimen compat_notification_large_icon_max_height 0x7f060005
int dimen compat_notification_large_icon_max_width 0x7f060006
int dimen exo_error_message_height 0x7f060007
int dimen exo_error_message_margin_bottom 0x7f060008
int dimen exo_error_message_text_padding_horizontal 0x7f060009
int dimen exo_error_message_text_padding_vertical 0x7f06000a
int dimen exo_error_message_text_size 0x7f06000b
int dimen exo_icon_horizontal_margin 0x7f06000c
int dimen exo_icon_padding 0x7f06000d
int dimen exo_icon_padding_bottom 0x7f06000e
int dimen exo_icon_size 0x7f06000f
int dimen exo_icon_text_size 0x7f060010
int dimen exo_media_button_height 0x7f060011
int dimen exo_media_button_width 0x7f060012
int dimen exo_setting_width 0x7f060013
int dimen exo_settings_height 0x7f060014
int dimen exo_settings_icon_size 0x7f060015
int dimen exo_settings_main_text_size 0x7f060016
int dimen exo_settings_offset 0x7f060017
int dimen exo_settings_sub_text_size 0x7f060018
int dimen exo_settings_text_height 0x7f060019
int dimen exo_small_icon_height 0x7f06001a
int dimen exo_small_icon_horizontal_margin 0x7f06001b
int dimen exo_small_icon_padding_horizontal 0x7f06001c
int dimen exo_small_icon_padding_vertical 0x7f06001d
int dimen exo_small_icon_width 0x7f06001e
int dimen exo_styled_bottom_bar_height 0x7f06001f
int dimen exo_styled_bottom_bar_margin_top 0x7f060020
int dimen exo_styled_bottom_bar_time_padding 0x7f060021
int dimen exo_styled_controls_padding 0x7f060022
int dimen exo_styled_minimal_controls_margin_bottom 0x7f060023
int dimen exo_styled_progress_bar_height 0x7f060024
int dimen exo_styled_progress_dragged_thumb_size 0x7f060025
int dimen exo_styled_progress_enabled_thumb_size 0x7f060026
int dimen exo_styled_progress_layout_height 0x7f060027
int dimen exo_styled_progress_margin_bottom 0x7f060028
int dimen exo_styled_progress_touch_target_height 0x7f060029
int dimen fastscroll_default_thickness 0x7f06002a
int dimen fastscroll_margin 0x7f06002b
int dimen fastscroll_minimum_range 0x7f06002c
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f06002d
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f06002e
int dimen item_touch_helper_swipe_escape_velocity 0x7f06002f
int dimen notification_action_icon_size 0x7f060030
int dimen notification_action_text_size 0x7f060031
int dimen notification_big_circle_margin 0x7f060032
int dimen notification_content_margin_start 0x7f060033
int dimen notification_large_icon_height 0x7f060034
int dimen notification_large_icon_width 0x7f060035
int dimen notification_main_column_padding_top 0x7f060036
int dimen notification_media_narrow_margin 0x7f060037
int dimen notification_right_icon_size 0x7f060038
int dimen notification_right_side_padding_top 0x7f060039
int dimen notification_small_icon_background_padding 0x7f06003a
int dimen notification_small_icon_size_as_large 0x7f06003b
int dimen notification_subtext_size 0x7f06003c
int dimen notification_top_pad 0x7f06003d
int dimen notification_top_pad_large_text 0x7f06003e
int drawable abc_vector_test 0x7f070001
int drawable exo_edit_mode_logo 0x7f070002
int drawable exo_ic_audiotrack 0x7f070003
int drawable exo_ic_check 0x7f070004
int drawable exo_ic_chevron_left 0x7f070005
int drawable exo_ic_chevron_right 0x7f070006
int drawable exo_ic_default_album_image 0x7f070007
int drawable exo_ic_forward 0x7f070008
int drawable exo_ic_fullscreen_enter 0x7f070009
int drawable exo_ic_fullscreen_exit 0x7f07000a
int drawable exo_ic_pause_circle_filled 0x7f07000b
int drawable exo_ic_play_circle_filled 0x7f07000c
int drawable exo_ic_rewind 0x7f07000d
int drawable exo_ic_settings 0x7f07000e
int drawable exo_ic_skip_next 0x7f07000f
int drawable exo_ic_skip_previous 0x7f070010
int drawable exo_ic_speed 0x7f070011
int drawable exo_ic_subtitle_off 0x7f070012
int drawable exo_ic_subtitle_on 0x7f070013
int drawable exo_icon_circular_play 0x7f070014
int drawable exo_icon_fastforward 0x7f070015
int drawable exo_icon_fullscreen_enter 0x7f070016
int drawable exo_icon_fullscreen_exit 0x7f070017
int drawable exo_icon_next 0x7f070018
int drawable exo_icon_pause 0x7f070019
int drawable exo_icon_play 0x7f07001a
int drawable exo_icon_previous 0x7f07001b
int drawable exo_icon_repeat_all 0x7f07001c
int drawable exo_icon_repeat_off 0x7f07001d
int drawable exo_icon_repeat_one 0x7f07001e
int drawable exo_icon_rewind 0x7f07001f
int drawable exo_icon_shuffle_off 0x7f070020
int drawable exo_icon_shuffle_on 0x7f070021
int drawable exo_icon_stop 0x7f070022
int drawable exo_icon_vr 0x7f070023
int drawable exo_legacy_controls_fastforward 0x7f070024
int drawable exo_legacy_controls_fullscreen_enter 0x7f070025
int drawable exo_legacy_controls_fullscreen_exit 0x7f070026
int drawable exo_legacy_controls_next 0x7f070027
int drawable exo_legacy_controls_pause 0x7f070028
int drawable exo_legacy_controls_play 0x7f070029
int drawable exo_legacy_controls_previous 0x7f07002a
int drawable exo_legacy_controls_repeat_all 0x7f07002b
int drawable exo_legacy_controls_repeat_off 0x7f07002c
int drawable exo_legacy_controls_repeat_one 0x7f07002d
int drawable exo_legacy_controls_rewind 0x7f07002e
int drawable exo_legacy_controls_shuffle_off 0x7f07002f
int drawable exo_legacy_controls_shuffle_on 0x7f070030
int drawable exo_legacy_controls_vr 0x7f070031
int drawable exo_notification_fastforward 0x7f070032
int drawable exo_notification_next 0x7f070033
int drawable exo_notification_pause 0x7f070034
int drawable exo_notification_play 0x7f070035
int drawable exo_notification_previous 0x7f070036
int drawable exo_notification_rewind 0x7f070037
int drawable exo_notification_small_icon 0x7f070038
int drawable exo_notification_stop 0x7f070039
int drawable exo_rounded_rectangle 0x7f07003a
int drawable exo_styled_controls_audiotrack 0x7f07003b
int drawable exo_styled_controls_check 0x7f07003c
int drawable exo_styled_controls_fastforward 0x7f07003d
int drawable exo_styled_controls_fullscreen_enter 0x7f07003e
int drawable exo_styled_controls_fullscreen_exit 0x7f07003f
int drawable exo_styled_controls_next 0x7f070040
int drawable exo_styled_controls_overflow_hide 0x7f070041
int drawable exo_styled_controls_overflow_show 0x7f070042
int drawable exo_styled_controls_pause 0x7f070043
int drawable exo_styled_controls_play 0x7f070044
int drawable exo_styled_controls_previous 0x7f070045
int drawable exo_styled_controls_repeat_all 0x7f070046
int drawable exo_styled_controls_repeat_off 0x7f070047
int drawable exo_styled_controls_repeat_one 0x7f070048
int drawable exo_styled_controls_rewind 0x7f070049
int drawable exo_styled_controls_settings 0x7f07004a
int drawable exo_styled_controls_shuffle_off 0x7f07004b
int drawable exo_styled_controls_shuffle_on 0x7f07004c
int drawable exo_styled_controls_speed 0x7f07004d
int drawable exo_styled_controls_subtitle_off 0x7f07004e
int drawable exo_styled_controls_subtitle_on 0x7f07004f
int drawable exo_styled_controls_vr 0x7f070050
int drawable ic_call_answer 0x7f070051
int drawable ic_call_answer_low 0x7f070052
int drawable ic_call_answer_video 0x7f070053
int drawable ic_call_answer_video_low 0x7f070054
int drawable ic_call_decline 0x7f070055
int drawable ic_call_decline_low 0x7f070056
int drawable ic_launcher_background 0x7f070057
int drawable ic_launcher_foreground 0x7f070058
int drawable notification_action_background 0x7f070059
int drawable notification_bg 0x7f07005a
int drawable notification_bg_low 0x7f07005b
int drawable notification_bg_low_normal 0x7f07005c
int drawable notification_bg_low_pressed 0x7f07005d
int drawable notification_bg_normal 0x7f07005e
int drawable notification_bg_normal_pressed 0x7f07005f
int drawable notification_icon_background 0x7f070060
int drawable notification_oversize_large_icon_bg 0x7f070061
int drawable notification_template_icon_bg 0x7f070062
int drawable notification_template_icon_low_bg 0x7f070063
int drawable notification_tile_bg 0x7f070064
int drawable notify_panel_notification_icon_bg 0x7f070065
int font roboto_medium_numbers 0x7f080000
int id accessibility_action_clickable_span 0x7f090000
int id accessibility_custom_action_0 0x7f090001
int id accessibility_custom_action_1 0x7f090002
int id accessibility_custom_action_10 0x7f090003
int id accessibility_custom_action_11 0x7f090004
int id accessibility_custom_action_12 0x7f090005
int id accessibility_custom_action_13 0x7f090006
int id accessibility_custom_action_14 0x7f090007
int id accessibility_custom_action_15 0x7f090008
int id accessibility_custom_action_16 0x7f090009
int id accessibility_custom_action_17 0x7f09000a
int id accessibility_custom_action_18 0x7f09000b
int id accessibility_custom_action_19 0x7f09000c
int id accessibility_custom_action_2 0x7f09000d
int id accessibility_custom_action_20 0x7f09000e
int id accessibility_custom_action_21 0x7f09000f
int id accessibility_custom_action_22 0x7f090010
int id accessibility_custom_action_23 0x7f090011
int id accessibility_custom_action_24 0x7f090012
int id accessibility_custom_action_25 0x7f090013
int id accessibility_custom_action_26 0x7f090014
int id accessibility_custom_action_27 0x7f090015
int id accessibility_custom_action_28 0x7f090016
int id accessibility_custom_action_29 0x7f090017
int id accessibility_custom_action_3 0x7f090018
int id accessibility_custom_action_30 0x7f090019
int id accessibility_custom_action_31 0x7f09001a
int id accessibility_custom_action_4 0x7f09001b
int id accessibility_custom_action_5 0x7f09001c
int id accessibility_custom_action_6 0x7f09001d
int id accessibility_custom_action_7 0x7f09001e
int id accessibility_custom_action_8 0x7f09001f
int id accessibility_custom_action_9 0x7f090020
int id action0 0x7f090021
int id action_container 0x7f090022
int id action_divider 0x7f090023
int id action_image 0x7f090024
int id action_text 0x7f090025
int id actions 0x7f090026
int id all 0x7f090027
int id always 0x7f090028
int id androidx_compose_ui_view_composition_context 0x7f090029
int id async 0x7f09002a
int id blocking 0x7f09002b
int id bottom 0x7f09002c
int id cancel_action 0x7f09002d
int id center 0x7f09002e
int id chronometer 0x7f09002f
int id coil_request_manager 0x7f090030
int id compose_view_saveable_id_tag 0x7f090031
int id consume_window_insets_tag 0x7f090032
int id dialog_button 0x7f090033
int id edit_text_id 0x7f090034
int id end_padder 0x7f090035
int id exo_ad_overlay 0x7f090036
int id exo_artwork 0x7f090037
int id exo_audio_track 0x7f090038
int id exo_basic_controls 0x7f090039
int id exo_bottom_bar 0x7f09003a
int id exo_buffering 0x7f09003b
int id exo_center_controls 0x7f09003c
int id exo_check 0x7f09003d
int id exo_content_frame 0x7f09003e
int id exo_controller 0x7f09003f
int id exo_controller_placeholder 0x7f090040
int id exo_controls_background 0x7f090041
int id exo_duration 0x7f090042
int id exo_error_message 0x7f090043
int id exo_extra_controls 0x7f090044
int id exo_extra_controls_scroll_view 0x7f090045
int id exo_ffwd 0x7f090046
int id exo_ffwd_with_amount 0x7f090047
int id exo_fullscreen 0x7f090048
int id exo_icon 0x7f090049
int id exo_main_text 0x7f09004a
int id exo_minimal_controls 0x7f09004b
int id exo_minimal_fullscreen 0x7f09004c
int id exo_next 0x7f09004d
int id exo_overflow_hide 0x7f09004e
int id exo_overflow_show 0x7f09004f
int id exo_overlay 0x7f090050
int id exo_pause 0x7f090051
int id exo_play 0x7f090052
int id exo_play_pause 0x7f090053
int id exo_playback_speed 0x7f090054
int id exo_position 0x7f090055
int id exo_prev 0x7f090056
int id exo_progress 0x7f090057
int id exo_progress_placeholder 0x7f090058
int id exo_repeat_toggle 0x7f090059
int id exo_rew 0x7f09005a
int id exo_rew_with_amount 0x7f09005b
int id exo_settings 0x7f09005c
int id exo_settings_listview 0x7f09005d
int id exo_shuffle 0x7f09005e
int id exo_shutter 0x7f09005f
int id exo_sub_text 0x7f090060
int id exo_subtitle 0x7f090061
int id exo_subtitles 0x7f090062
int id exo_text 0x7f090063
int id exo_time 0x7f090064
int id exo_track_selection_view 0x7f090065
int id exo_vr 0x7f090066
int id fill 0x7f090067
int id fit 0x7f090068
int id fixed_height 0x7f090069
int id fixed_width 0x7f09006a
int id forever 0x7f09006b
int id fragment_container_view_tag 0x7f09006c
int id hide_graphics_layer_in_inspector_tag 0x7f09006d
int id hide_ime_id 0x7f09006e
int id hide_in_inspector_tag 0x7f09006f
int id icon 0x7f090070
int id icon_group 0x7f090071
int id info 0x7f090072
int id inspection_slot_table_set 0x7f090073
int id is_pooling_container_tag 0x7f090074
int id italic 0x7f090075
int id item_touch_helper_previous_elevation 0x7f090076
int id line1 0x7f090077
int id line3 0x7f090078
int id media_actions 0x7f090079
int id media_controller_compat_view_tag 0x7f09007a
int id nav_controller_view_tag 0x7f09007b
int id never 0x7f09007c
int id none 0x7f09007d
int id normal 0x7f09007e
int id notification_background 0x7f09007f
int id notification_main_column 0x7f090080
int id notification_main_column_container 0x7f090081
int id off 0x7f090082
int id one 0x7f090083
int id pooling_container_listener_holder_tag 0x7f090084
int id report_drawn 0x7f090085
int id right_icon 0x7f090086
int id right_side 0x7f090087
int id special_effects_controller_view_tag 0x7f090088
int id spherical_gl_surface_view 0x7f090089
int id status_bar_latest_event_content 0x7f09008a
int id surface_view 0x7f09008b
int id tag_accessibility_actions 0x7f09008c
int id tag_accessibility_clickable_spans 0x7f09008d
int id tag_accessibility_heading 0x7f09008e
int id tag_accessibility_pane_title 0x7f09008f
int id tag_compat_insets_dispatch 0x7f090090
int id tag_on_apply_window_listener 0x7f090091
int id tag_on_receive_content_listener 0x7f090092
int id tag_on_receive_content_mime_types 0x7f090093
int id tag_screen_reader_focusable 0x7f090094
int id tag_state_description 0x7f090095
int id tag_system_bar_state_monitor 0x7f090096
int id tag_transition_group 0x7f090097
int id tag_unhandled_key_event_manager 0x7f090098
int id tag_unhandled_key_listeners 0x7f090099
int id tag_window_insets_animation_callback 0x7f09009a
int id text 0x7f09009b
int id text2 0x7f09009c
int id texture_view 0x7f09009d
int id time 0x7f09009e
int id title 0x7f09009f
int id video_decoder_gl_surface_view 0x7f0900a0
int id view_tree_disjoint_parent 0x7f0900a1
int id view_tree_lifecycle_owner 0x7f0900a2
int id view_tree_on_back_pressed_dispatcher_owner 0x7f0900a3
int id view_tree_saved_state_registry_owner 0x7f0900a4
int id view_tree_view_model_store_owner 0x7f0900a5
int id visible_removing_fragment_view_tag 0x7f0900a6
int id when_playing 0x7f0900a7
int id wrapped_composition_tag 0x7f0900a8
int id zoom 0x7f0900a9
int integer cancel_button_image_alpha 0x7f0a0000
int integer exo_media_button_opacity_percentage_disabled 0x7f0a0001
int integer exo_media_button_opacity_percentage_enabled 0x7f0a0002
int integer m3c_window_layout_in_display_cutout_mode 0x7f0a0003
int integer status_bar_notification_info_maxnum 0x7f0a0004
int layout custom_dialog 0x7f0b0000
int layout exo_legacy_player_control_view 0x7f0b0001
int layout exo_list_divider 0x7f0b0002
int layout exo_player_control_ffwd_button 0x7f0b0003
int layout exo_player_control_rewind_button 0x7f0b0004
int layout exo_player_control_view 0x7f0b0005
int layout exo_player_view 0x7f0b0006
int layout exo_styled_settings_list 0x7f0b0007
int layout exo_styled_settings_list_item 0x7f0b0008
int layout exo_styled_sub_settings_list_item 0x7f0b0009
int layout exo_track_selection_dialog 0x7f0b000a
int layout ime_base_split_test_activity 0x7f0b000b
int layout ime_secondary_split_test_activity 0x7f0b000c
int layout notification_action 0x7f0b000d
int layout notification_action_tombstone 0x7f0b000e
int layout notification_media_action 0x7f0b000f
int layout notification_media_cancel_action 0x7f0b0010
int layout notification_template_big_media 0x7f0b0011
int layout notification_template_big_media_custom 0x7f0b0012
int layout notification_template_big_media_narrow 0x7f0b0013
int layout notification_template_big_media_narrow_custom 0x7f0b0014
int layout notification_template_custom_big 0x7f0b0015
int layout notification_template_icon_group 0x7f0b0016
int layout notification_template_lines_media 0x7f0b0017
int layout notification_template_media 0x7f0b0018
int layout notification_template_media_custom 0x7f0b0019
int layout notification_template_part_chronometer 0x7f0b001a
int layout notification_template_part_time 0x7f0b001b
int mipmap ic_launcher 0x7f0c0000
int mipmap ic_launcher_round 0x7f0c0001
int plurals exo_controls_fastforward_by_amount_description 0x7f0d0000
int plurals exo_controls_rewind_by_amount_description 0x7f0d0001
int string androidx_startup 0x7f0e0000
int string app_name 0x7f0e0001
int string call_notification_answer_action 0x7f0e0002
int string call_notification_answer_video_action 0x7f0e0003
int string call_notification_decline_action 0x7f0e0004
int string call_notification_hang_up_action 0x7f0e0005
int string call_notification_incoming_text 0x7f0e0006
int string call_notification_ongoing_text 0x7f0e0007
int string call_notification_screening_text 0x7f0e0008
int string close_drawer 0x7f0e0009
int string close_sheet 0x7f0e000a
int string default_error_message 0x7f0e000b
int string default_popup_window_title 0x7f0e000c
int string dropdown_menu 0x7f0e000d
int string exo_controls_cc_disabled_description 0x7f0e000e
int string exo_controls_cc_enabled_description 0x7f0e000f
int string exo_controls_custom_playback_speed 0x7f0e0010
int string exo_controls_fastforward_description 0x7f0e0011
int string exo_controls_fullscreen_enter_description 0x7f0e0012
int string exo_controls_fullscreen_exit_description 0x7f0e0013
int string exo_controls_hide 0x7f0e0014
int string exo_controls_next_description 0x7f0e0015
int string exo_controls_overflow_hide_description 0x7f0e0016
int string exo_controls_overflow_show_description 0x7f0e0017
int string exo_controls_pause_description 0x7f0e0018
int string exo_controls_play_description 0x7f0e0019
int string exo_controls_playback_speed 0x7f0e001a
int string exo_controls_previous_description 0x7f0e001b
int string exo_controls_repeat_all_description 0x7f0e001c
int string exo_controls_repeat_off_description 0x7f0e001d
int string exo_controls_repeat_one_description 0x7f0e001e
int string exo_controls_rewind_description 0x7f0e001f
int string exo_controls_seek_bar_description 0x7f0e0020
int string exo_controls_settings_description 0x7f0e0021
int string exo_controls_show 0x7f0e0022
int string exo_controls_shuffle_off_description 0x7f0e0023
int string exo_controls_shuffle_on_description 0x7f0e0024
int string exo_controls_stop_description 0x7f0e0025
int string exo_controls_time_placeholder 0x7f0e0026
int string exo_controls_vr_description 0x7f0e0027
int string exo_download_completed 0x7f0e0028
int string exo_download_description 0x7f0e0029
int string exo_download_downloading 0x7f0e002a
int string exo_download_failed 0x7f0e002b
int string exo_download_notification_channel_name 0x7f0e002c
int string exo_download_paused 0x7f0e002d
int string exo_download_paused_for_network 0x7f0e002e
int string exo_download_paused_for_wifi 0x7f0e002f
int string exo_download_removing 0x7f0e0030
int string exo_item_list 0x7f0e0031
int string exo_track_bitrate 0x7f0e0032
int string exo_track_mono 0x7f0e0033
int string exo_track_resolution 0x7f0e0034
int string exo_track_role_alternate 0x7f0e0035
int string exo_track_role_closed_captions 0x7f0e0036
int string exo_track_role_commentary 0x7f0e0037
int string exo_track_role_supplementary 0x7f0e0038
int string exo_track_selection_auto 0x7f0e0039
int string exo_track_selection_none 0x7f0e003a
int string exo_track_selection_title_audio 0x7f0e003b
int string exo_track_selection_title_text 0x7f0e003c
int string exo_track_selection_title_video 0x7f0e003d
int string exo_track_stereo 0x7f0e003e
int string exo_track_surround 0x7f0e003f
int string exo_track_surround_5_point_1 0x7f0e0040
int string exo_track_surround_7_point_1 0x7f0e0041
int string exo_track_unknown 0x7f0e0042
int string in_progress 0x7f0e0043
int string indeterminate 0x7f0e0044
int string m3c_bottom_sheet_collapse_description 0x7f0e0045
int string m3c_bottom_sheet_dismiss_description 0x7f0e0046
int string m3c_bottom_sheet_drag_handle_description 0x7f0e0047
int string m3c_bottom_sheet_expand_description 0x7f0e0048
int string m3c_bottom_sheet_pane_title 0x7f0e0049
int string m3c_date_input_headline 0x7f0e004a
int string m3c_date_input_headline_description 0x7f0e004b
int string m3c_date_input_invalid_for_pattern 0x7f0e004c
int string m3c_date_input_invalid_not_allowed 0x7f0e004d
int string m3c_date_input_invalid_year_range 0x7f0e004e
int string m3c_date_input_label 0x7f0e004f
int string m3c_date_input_no_input_description 0x7f0e0050
int string m3c_date_input_title 0x7f0e0051
int string m3c_date_picker_headline 0x7f0e0052
int string m3c_date_picker_headline_description 0x7f0e0053
int string m3c_date_picker_navigate_to_year_description 0x7f0e0054
int string m3c_date_picker_no_selection_description 0x7f0e0055
int string m3c_date_picker_scroll_to_earlier_years 0x7f0e0056
int string m3c_date_picker_scroll_to_later_years 0x7f0e0057
int string m3c_date_picker_switch_to_calendar_mode 0x7f0e0058
int string m3c_date_picker_switch_to_day_selection 0x7f0e0059
int string m3c_date_picker_switch_to_input_mode 0x7f0e005a
int string m3c_date_picker_switch_to_next_month 0x7f0e005b
int string m3c_date_picker_switch_to_previous_month 0x7f0e005c
int string m3c_date_picker_switch_to_year_selection 0x7f0e005d
int string m3c_date_picker_title 0x7f0e005e
int string m3c_date_picker_today_description 0x7f0e005f
int string m3c_date_picker_year_picker_pane_title 0x7f0e0060
int string m3c_date_range_input_invalid_range_input 0x7f0e0061
int string m3c_date_range_input_title 0x7f0e0062
int string m3c_date_range_picker_day_in_range 0x7f0e0063
int string m3c_date_range_picker_end_headline 0x7f0e0064
int string m3c_date_range_picker_scroll_to_next_month 0x7f0e0065
int string m3c_date_range_picker_scroll_to_previous_month 0x7f0e0066
int string m3c_date_range_picker_start_headline 0x7f0e0067
int string m3c_date_range_picker_title 0x7f0e0068
int string m3c_dialog 0x7f0e0069
int string m3c_dropdown_menu_collapsed 0x7f0e006a
int string m3c_dropdown_menu_expanded 0x7f0e006b
int string m3c_dropdown_menu_toggle 0x7f0e006c
int string m3c_search_bar_search 0x7f0e006d
int string m3c_snackbar_dismiss 0x7f0e006e
int string m3c_suggestions_available 0x7f0e006f
int string m3c_time_picker_am 0x7f0e0070
int string m3c_time_picker_hour 0x7f0e0071
int string m3c_time_picker_hour_24h_suffix 0x7f0e0072
int string m3c_time_picker_hour_selection 0x7f0e0073
int string m3c_time_picker_hour_suffix 0x7f0e0074
int string m3c_time_picker_hour_text_field 0x7f0e0075
int string m3c_time_picker_minute 0x7f0e0076
int string m3c_time_picker_minute_selection 0x7f0e0077
int string m3c_time_picker_minute_suffix 0x7f0e0078
int string m3c_time_picker_minute_text_field 0x7f0e0079
int string m3c_time_picker_period_toggle_description 0x7f0e007a
int string m3c_time_picker_pm 0x7f0e007b
int string m3c_tooltip_long_press_label 0x7f0e007c
int string m3c_tooltip_pane_description 0x7f0e007d
int string navigation_menu 0x7f0e007e
int string not_selected 0x7f0e007f
int string range_end 0x7f0e0080
int string range_start 0x7f0e0081
int string selected 0x7f0e0082
int string state_empty 0x7f0e0083
int string state_off 0x7f0e0084
int string state_on 0x7f0e0085
int string status_bar_notification_info_overflow 0x7f0e0086
int string switch_role 0x7f0e0087
int string tab 0x7f0e0088
int string template_percent 0x7f0e0089
int string tooltip_description 0x7f0e008a
int string tooltip_label 0x7f0e008b
int style DialogWindowTheme 0x7f0f0000
int style EdgeToEdgeFloatingDialogTheme 0x7f0f0001
int style EdgeToEdgeFloatingDialogWindowTheme 0x7f0f0002
int style ExoMediaButton 0x7f0f0003
int style ExoMediaButton_FastForward 0x7f0f0004
int style ExoMediaButton_Next 0x7f0f0005
int style ExoMediaButton_Pause 0x7f0f0006
int style ExoMediaButton_Play 0x7f0f0007
int style ExoMediaButton_Previous 0x7f0f0008
int style ExoMediaButton_Rewind 0x7f0f0009
int style ExoMediaButton_VR 0x7f0f000a
int style ExoStyledControls 0x7f0f000b
int style ExoStyledControls_Button 0x7f0f000c
int style ExoStyledControls_Button_Bottom 0x7f0f000d
int style ExoStyledControls_Button_Bottom_AudioTrack 0x7f0f000e
int style ExoStyledControls_Button_Bottom_CC 0x7f0f000f
int style ExoStyledControls_Button_Bottom_FullScreen 0x7f0f0010
int style ExoStyledControls_Button_Bottom_OverflowHide 0x7f0f0011
int style ExoStyledControls_Button_Bottom_OverflowShow 0x7f0f0012
int style ExoStyledControls_Button_Bottom_PlaybackSpeed 0x7f0f0013
int style ExoStyledControls_Button_Bottom_RepeatToggle 0x7f0f0014
int style ExoStyledControls_Button_Bottom_Settings 0x7f0f0015
int style ExoStyledControls_Button_Bottom_Shuffle 0x7f0f0016
int style ExoStyledControls_Button_Bottom_VR 0x7f0f0017
int style ExoStyledControls_Button_Center 0x7f0f0018
int style ExoStyledControls_Button_Center_FfwdWithAmount 0x7f0f0019
int style ExoStyledControls_Button_Center_Next 0x7f0f001a
int style ExoStyledControls_Button_Center_PlayPause 0x7f0f001b
int style ExoStyledControls_Button_Center_Previous 0x7f0f001c
int style ExoStyledControls_Button_Center_RewWithAmount 0x7f0f001d
int style ExoStyledControls_TimeBar 0x7f0f001e
int style ExoStyledControls_TimeText 0x7f0f001f
int style ExoStyledControls_TimeText_Duration 0x7f0f0020
int style ExoStyledControls_TimeText_Position 0x7f0f0021
int style ExoStyledControls_TimeText_Separator 0x7f0f0022
int style FloatingDialogTheme 0x7f0f0023
int style FloatingDialogWindowTheme 0x7f0f0024
int style TextAppearance_Compat_Notification 0x7f0f0025
int style TextAppearance_Compat_Notification_Info 0x7f0f0026
int style TextAppearance_Compat_Notification_Info_Media 0x7f0f0027
int style TextAppearance_Compat_Notification_Line2 0x7f0f0028
int style TextAppearance_Compat_Notification_Line2_Media 0x7f0f0029
int style TextAppearance_Compat_Notification_Media 0x7f0f002a
int style TextAppearance_Compat_Notification_Time 0x7f0f002b
int style TextAppearance_Compat_Notification_Time_Media 0x7f0f002c
int style TextAppearance_Compat_Notification_Title 0x7f0f002d
int style TextAppearance_Compat_Notification_Title_Media 0x7f0f002e
int style Theme_Shipin 0x7f0f002f
int style Widget_Compat_NotificationActionContainer 0x7f0f0030
int style Widget_Compat_NotificationActionText 0x7f0f0031
int[] styleable ActivityNavigator { 0x01010003, 0x7f040000, 0x7f04000d, 0x7f04000e, 0x7f040052 }
int styleable ActivityNavigator_android_name 0
int styleable ActivityNavigator_action 1
int styleable ActivityNavigator_data 2
int styleable ActivityNavigator_dataPattern 3
int styleable ActivityNavigator_targetPackage 4
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AspectRatioFrameLayout { 0x7f04003a }
int styleable AspectRatioFrameLayout_resize_mode 0
int[] styleable Capability { 0x7f040037, 0x7f040043 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f040003, 0x7f040028 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable DefaultTimeBar { 0x7f040001, 0x7f040002, 0x7f040009, 0x7f04000a, 0x7f04000b, 0x7f04002f, 0x7f040030, 0x7f04003e, 0x7f04003f, 0x7f040040, 0x7f040041, 0x7f040042, 0x7f040054, 0x7f040056 }
int styleable DefaultTimeBar_ad_marker_color 0
int styleable DefaultTimeBar_ad_marker_width 1
int styleable DefaultTimeBar_bar_gravity 2
int styleable DefaultTimeBar_bar_height 3
int styleable DefaultTimeBar_buffered_color 4
int styleable DefaultTimeBar_played_ad_marker_color 5
int styleable DefaultTimeBar_played_color 6
int styleable DefaultTimeBar_scrubber_color 7
int styleable DefaultTimeBar_scrubber_disabled_size 8
int styleable DefaultTimeBar_scrubber_dragged_size 9
int styleable DefaultTimeBar_scrubber_drawable 10
int styleable DefaultTimeBar_scrubber_enabled_size 11
int styleable DefaultTimeBar_touch_target_height 12
int styleable DefaultTimeBar_unplayed_color 13
int[] styleable FontFamily { 0x7f040019, 0x7f04001a, 0x7f04001b, 0x7f04001c, 0x7f04001d, 0x7f04001e, 0x7f04001f, 0x7f040020 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFallbackQuery 2
int styleable FontFamily_fontProviderFetchStrategy 3
int styleable FontFamily_fontProviderFetchTimeout 4
int styleable FontFamily_fontProviderPackage 5
int styleable FontFamily_fontProviderQuery 6
int styleable FontFamily_fontProviderSystemFontFamily 7
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f040018, 0x7f040021, 0x7f040022, 0x7f040023, 0x7f040055 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable LegacyPlayerControlView { 0x7f040001, 0x7f040002, 0x7f040009, 0x7f04000a, 0x7f04000b, 0x7f04000c, 0x7f04002f, 0x7f040030, 0x7f040039, 0x7f04003e, 0x7f04003f, 0x7f040040, 0x7f040041, 0x7f040042, 0x7f040045, 0x7f040046, 0x7f040047, 0x7f040048, 0x7f040049, 0x7f04004b, 0x7f040053, 0x7f040054, 0x7f040056 }
int styleable LegacyPlayerControlView_ad_marker_color 0
int styleable LegacyPlayerControlView_ad_marker_width 1
int styleable LegacyPlayerControlView_bar_gravity 2
int styleable LegacyPlayerControlView_bar_height 3
int styleable LegacyPlayerControlView_buffered_color 4
int styleable LegacyPlayerControlView_controller_layout_id 5
int styleable LegacyPlayerControlView_played_ad_marker_color 6
int styleable LegacyPlayerControlView_played_color 7
int styleable LegacyPlayerControlView_repeat_toggle_modes 8
int styleable LegacyPlayerControlView_scrubber_color 9
int styleable LegacyPlayerControlView_scrubber_disabled_size 10
int styleable LegacyPlayerControlView_scrubber_dragged_size 11
int styleable LegacyPlayerControlView_scrubber_drawable 12
int styleable LegacyPlayerControlView_scrubber_enabled_size 13
int styleable LegacyPlayerControlView_show_fastforward_button 14
int styleable LegacyPlayerControlView_show_next_button 15
int styleable LegacyPlayerControlView_show_previous_button 16
int styleable LegacyPlayerControlView_show_rewind_button 17
int styleable LegacyPlayerControlView_show_shuffle_button 18
int styleable LegacyPlayerControlView_show_timeout 19
int styleable LegacyPlayerControlView_time_bar_min_update_interval 20
int styleable LegacyPlayerControlView_touch_target_height 21
int styleable LegacyPlayerControlView_unplayed_color 22
int[] styleable NavAction { 0x010100d0, 0x7f040010, 0x7f040011, 0x7f040012, 0x7f040029, 0x7f040032, 0x7f040033, 0x7f040034, 0x7f040035, 0x7f040036, 0x7f04003b }
int styleable NavAction_android_id 0
int styleable NavAction_destination 1
int styleable NavAction_enterAnim 2
int styleable NavAction_exitAnim 3
int styleable NavAction_launchSingleTop 4
int styleable NavAction_popEnterAnim 5
int styleable NavAction_popExitAnim 6
int styleable NavAction_popUpTo 7
int styleable NavAction_popUpToInclusive 8
int styleable NavAction_popUpToSaveState 9
int styleable NavAction_restoreState 10
int[] styleable NavArgument { 0x01010003, 0x010101ed, 0x7f040005, 0x7f04002e }
int styleable NavArgument_android_name 0
int styleable NavArgument_android_defaultValue 1
int styleable NavArgument_argType 2
int styleable NavArgument_nullable 3
int[] styleable NavDeepLink { 0x010104ee, 0x7f040000, 0x7f04002b, 0x7f040057 }
int styleable NavDeepLink_android_autoVerify 0
int styleable NavDeepLink_action 1
int styleable NavDeepLink_mimeType 2
int styleable NavDeepLink_uri 3
int[] styleable NavGraphNavigator { 0x7f040050 }
int styleable NavGraphNavigator_startDestination 0
int[] styleable NavHost { 0x7f04002c }
int styleable NavHost_navGraph 0
int[] styleable NavInclude { 0x7f040024 }
int styleable NavInclude_graph 0
int[] styleable Navigator { 0x01010001, 0x010100d0, 0x7f04003d }
int styleable Navigator_android_label 0
int styleable Navigator_android_id 1
int styleable Navigator_route 2
int[] styleable PlayerControlView { 0x7f040001, 0x7f040002, 0x7f040004, 0x7f040009, 0x7f04000a, 0x7f04000b, 0x7f04000c, 0x7f04002f, 0x7f040030, 0x7f040039, 0x7f04003e, 0x7f04003f, 0x7f040040, 0x7f040041, 0x7f040042, 0x7f040045, 0x7f040046, 0x7f040047, 0x7f040048, 0x7f040049, 0x7f04004a, 0x7f04004b, 0x7f04004c, 0x7f040053, 0x7f040054, 0x7f040056 }
int styleable PlayerControlView_ad_marker_color 0
int styleable PlayerControlView_ad_marker_width 1
int styleable PlayerControlView_animation_enabled 2
int styleable PlayerControlView_bar_gravity 3
int styleable PlayerControlView_bar_height 4
int styleable PlayerControlView_buffered_color 5
int styleable PlayerControlView_controller_layout_id 6
int styleable PlayerControlView_played_ad_marker_color 7
int styleable PlayerControlView_played_color 8
int styleable PlayerControlView_repeat_toggle_modes 9
int styleable PlayerControlView_scrubber_color 10
int styleable PlayerControlView_scrubber_disabled_size 11
int styleable PlayerControlView_scrubber_dragged_size 12
int styleable PlayerControlView_scrubber_drawable 13
int styleable PlayerControlView_scrubber_enabled_size 14
int styleable PlayerControlView_show_fastforward_button 15
int styleable PlayerControlView_show_next_button 16
int styleable PlayerControlView_show_previous_button 17
int styleable PlayerControlView_show_rewind_button 18
int styleable PlayerControlView_show_shuffle_button 19
int styleable PlayerControlView_show_subtitle_button 20
int styleable PlayerControlView_show_timeout 21
int styleable PlayerControlView_show_vr_button 22
int styleable PlayerControlView_time_bar_min_update_interval 23
int styleable PlayerControlView_touch_target_height 24
int styleable PlayerControlView_unplayed_color 25
int[] styleable PlayerView { 0x7f040001, 0x7f040002, 0x7f040004, 0x7f040006, 0x7f040007, 0x7f040009, 0x7f04000a, 0x7f04000b, 0x7f04000c, 0x7f04000f, 0x7f040025, 0x7f040026, 0x7f040027, 0x7f04002f, 0x7f040030, 0x7f040031, 0x7f040039, 0x7f04003a, 0x7f04003e, 0x7f04003f, 0x7f040040, 0x7f040041, 0x7f040042, 0x7f040044, 0x7f040049, 0x7f04004a, 0x7f04004b, 0x7f04004c, 0x7f04004d, 0x7f040051, 0x7f040053, 0x7f040054, 0x7f040056, 0x7f040058, 0x7f040059 }
int styleable PlayerView_ad_marker_color 0
int styleable PlayerView_ad_marker_width 1
int styleable PlayerView_animation_enabled 2
int styleable PlayerView_artwork_display_mode 3
int styleable PlayerView_auto_show 4
int styleable PlayerView_bar_gravity 5
int styleable PlayerView_bar_height 6
int styleable PlayerView_buffered_color 7
int styleable PlayerView_controller_layout_id 8
int styleable PlayerView_default_artwork 9
int styleable PlayerView_hide_during_ads 10
int styleable PlayerView_hide_on_touch 11
int styleable PlayerView_keep_content_on_player_reset 12
int styleable PlayerView_played_ad_marker_color 13
int styleable PlayerView_played_color 14
int styleable PlayerView_player_layout_id 15
int styleable PlayerView_repeat_toggle_modes 16
int styleable PlayerView_resize_mode 17
int styleable PlayerView_scrubber_color 18
int styleable PlayerView_scrubber_disabled_size 19
int styleable PlayerView_scrubber_dragged_size 20
int styleable PlayerView_scrubber_drawable 21
int styleable PlayerView_scrubber_enabled_size 22
int styleable PlayerView_show_buffering 23
int styleable PlayerView_show_shuffle_button 24
int styleable PlayerView_show_subtitle_button 25
int styleable PlayerView_show_timeout 26
int styleable PlayerView_show_vr_button 27
int styleable PlayerView_shutter_background_color 28
int styleable PlayerView_surface_type 29
int styleable PlayerView_time_bar_min_update_interval 30
int styleable PlayerView_touch_target_height 31
int styleable PlayerView_unplayed_color 32
int styleable PlayerView_use_artwork 33
int styleable PlayerView_use_controller 34
int[] styleable RecyclerView { 0x010100c4, 0x010100eb, 0x010100f1, 0x7f040013, 0x7f040014, 0x7f040015, 0x7f040016, 0x7f040017, 0x7f04002a, 0x7f04003c, 0x7f04004e, 0x7f04004f }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_clipToPadding 1
int styleable RecyclerView_android_descendantFocusability 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int xml backup_rules 0x7f110000
int xml data_extraction_rules 0x7f110001
