package com.example.shipin.data.api;

/**
 * 视频信息数据模型
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b*\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u0099\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u0010J\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010 \u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010#\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010$\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010%\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010&\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\'\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010(\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010)\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010*\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010+\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u00a1\u0001\u0010,\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010-\u001a\u00020.2\b\u0010/\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00100\u001a\u000201H\u00d6\u0001J\t\u00102\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u000f\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0013\u0010\u000e\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0012R\u0013\u0010\r\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0012R\u0013\u0010\n\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0012R\u0013\u0010\b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0012R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0012R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0012R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0012R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0012R\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0012R\u0013\u0010\f\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0012R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0012R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0012\u00a8\u00063"}, d2 = {"Lcom/example/shipin/data/api/VideoItem;", "", "vod_id", "", "vod_name", "vod_pic", "vod_remarks", "vod_year", "vod_area", "vod_director", "vod_actor", "vod_content", "vod_play_url", "type_name", "source_name", "source_code", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getSource_code", "()Ljava/lang/String;", "getSource_name", "getType_name", "getVod_actor", "getVod_area", "getVod_content", "getVod_director", "getVod_id", "getVod_name", "getVod_pic", "getVod_play_url", "getVod_remarks", "getVod_year", "component1", "component10", "component11", "component12", "component13", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class VideoItem {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String vod_id = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String vod_name = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String vod_pic = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String vod_remarks = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String vod_year = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String vod_area = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String vod_director = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String vod_actor = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String vod_content = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String vod_play_url = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String type_name = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String source_name = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String source_code = null;
    
    public VideoItem(@org.jetbrains.annotations.NotNull()
    java.lang.String vod_id, @org.jetbrains.annotations.NotNull()
    java.lang.String vod_name, @org.jetbrains.annotations.Nullable()
    java.lang.String vod_pic, @org.jetbrains.annotations.Nullable()
    java.lang.String vod_remarks, @org.jetbrains.annotations.Nullable()
    java.lang.String vod_year, @org.jetbrains.annotations.Nullable()
    java.lang.String vod_area, @org.jetbrains.annotations.Nullable()
    java.lang.String vod_director, @org.jetbrains.annotations.Nullable()
    java.lang.String vod_actor, @org.jetbrains.annotations.Nullable()
    java.lang.String vod_content, @org.jetbrains.annotations.Nullable()
    java.lang.String vod_play_url, @org.jetbrains.annotations.Nullable()
    java.lang.String type_name, @org.jetbrains.annotations.Nullable()
    java.lang.String source_name, @org.jetbrains.annotations.Nullable()
    java.lang.String source_code) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getVod_id() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getVod_name() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getVod_pic() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getVod_remarks() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getVod_year() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getVod_area() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getVod_director() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getVod_actor() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getVod_content() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getVod_play_url() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getType_name() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSource_name() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSource_code() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.shipin.data.api.VideoItem copy(@org.jetbrains.annotations.NotNull()
    java.lang.String vod_id, @org.jetbrains.annotations.NotNull()
    java.lang.String vod_name, @org.jetbrains.annotations.Nullable()
    java.lang.String vod_pic, @org.jetbrains.annotations.Nullable()
    java.lang.String vod_remarks, @org.jetbrains.annotations.Nullable()
    java.lang.String vod_year, @org.jetbrains.annotations.Nullable()
    java.lang.String vod_area, @org.jetbrains.annotations.Nullable()
    java.lang.String vod_director, @org.jetbrains.annotations.Nullable()
    java.lang.String vod_actor, @org.jetbrains.annotations.Nullable()
    java.lang.String vod_content, @org.jetbrains.annotations.Nullable()
    java.lang.String vod_play_url, @org.jetbrains.annotations.Nullable()
    java.lang.String type_name, @org.jetbrains.annotations.Nullable()
    java.lang.String source_name, @org.jetbrains.annotations.Nullable()
    java.lang.String source_code) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}