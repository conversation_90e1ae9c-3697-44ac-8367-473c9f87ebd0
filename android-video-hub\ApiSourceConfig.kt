package com.videohub.data.api

/**
 * 视频API源配置
 * 学习LibreTV的多源聚合思路
 */
data class ApiSource(
    val id: String,
    val name: String,
    val baseUrl: String,
    val isAdult: Boolean = false,
    val priority: Int = 1,
    val isEnabled: Boolean = true
)

object ApiSourceConfig {
    
    // 主要视频源配置（学习LibreTV的API_SITES配置）
    val DEFAULT_SOURCES = listOf(
        ApiSource(
            id = "dyttzy",
            name = "电影天堂资源",
            baseUrl = "http://caiji.dyttzyapi.com/api.php/provide/vod",
            priority = 1
        ),
        ApiSource(
            id = "ruyi",
            name = "如意资源", 
            baseUrl = "https://cj.rycjapi.com/api.php/provide/vod",
            priority = 2
        ),
        ApiSource(
            id = "bfzy",
            name = "暴风资源",
            baseUrl = "https://bfzyapi.com/api.php/provide/vod",
            priority = 3
        ),
        ApiSource(
            id = "tyyszy",
            name = "天涯资源",
            baseUrl = "https://tyyszy.com/api.php/provide/vod",
            priority = 4
        ),
        ApiSource(
            id = "ffzy",
            name = "非凡影视",
            baseUrl = "http://ffzy5.tv/api.php/provide/vod",
            priority = 5
        )
    )
    
    // API请求配置（学习LibreTV的API_CONFIG）
    object RequestConfig {
        const val SEARCH_PATH = "?ac=videolist&wd="
        const val DETAIL_PATH = "?ac=videolist&ids="
        const val PAGE_PATH = "?ac=videolist&wd={query}&pg={page}"
        const val MAX_PAGES = 3  // 限制页数避免过多请求
        const val TIMEOUT_SECONDS = 8L
        const val MAX_CONCURRENT_REQUESTS = 5
        
        val DEFAULT_HEADERS = mapOf(
            "User-Agent" to "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36",
            "Accept" to "application/json"
        )
    }
    
    // 内容过滤配置（学习LibreTV的内容过滤机制）
    object ContentFilter {
        val ADULT_KEYWORDS = listOf(
            "伦理片", "福利", "里番动漫", "门事件", "萝莉少女", 
            "制服诱惑", "国产传媒", "cosplay", "黑丝诱惑",
            "无码", "日本无码", "有码", "日本有码", "SWAG",
            "网红主播", "色情片", "同性片", "福利视频", "福利片"
        )
        
        val QUALITY_KEYWORDS = listOf(
            "高清", "蓝光", "4K", "1080P", "720P", "超清"
        )
    }
}

/**
 * 自定义API源数据类
 */
data class CustomApiSource(
    val name: String,
    val url: String,
    val isAdult: Boolean = false,
    val isEnabled: Boolean = true,
    val addedTime: Long = System.currentTimeMillis()
)

/**
 * API响应数据模型（学习LibreTV的数据结构）
 */
data class ApiResponse<T>(
    val code: Int,
    val msg: String? = null,
    val list: List<T> = emptyList(),
    val total: Int = 0,
    val pagecount: Int = 1
)

/**
 * 视频信息数据模型
 */
data class VideoItem(
    val vod_id: String,
    val vod_name: String,
    val vod_pic: String? = null,
    val vod_remarks: String? = null,
    val vod_year: String? = null,
    val vod_area: String? = null,
    val vod_director: String? = null,
    val vod_actor: String? = null,
    val vod_content: String? = null,
    val vod_play_url: String? = null,
    val type_name: String? = null,
    val source_name: String? = null,
    val source_code: String? = null
)

/**
 * 视频详情数据模型
 */
data class VideoDetail(
    val videoInfo: VideoItem,
    val episodes: List<Episode>
)

/**
 * 剧集信息
 */
data class Episode(
    val index: Int,
    val name: String,
    val url: String
)
