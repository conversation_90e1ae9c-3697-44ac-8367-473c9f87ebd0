package com.example.shipin.presentation.search;

/**
 * 搜索ViewModel - 学习LibreTV的搜索逻辑
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\b\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0011\u001a\u00020\u0012J\u0006\u0010\u0013\u001a\u00020\u0012J\u0010\u0010\u0014\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\bH\u0002J\u000e\u0010\u0016\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\bJ\u0006\u0010\u0017\u001a\u00020\u0012J\u0006\u0010\u0018\u001a\u00020\u0012J\u000e\u0010\u0019\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\bR\u001a\u0010\u0005\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\n0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000eR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001a"}, d2 = {"Lcom/example/shipin/presentation/search/SearchViewModel;", "Landroidx/lifecycle/ViewModel;", "videoRepository", "Lcom/example/shipin/data/repository/VideoRepository;", "(Lcom/example/shipin/data/repository/VideoRepository;)V", "_searchHistory", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "", "_uiState", "Lcom/example/shipin/presentation/search/SearchUiState;", "searchHistory", "Lkotlinx/coroutines/flow/StateFlow;", "getSearchHistory", "()Lkotlinx/coroutines/flow/StateFlow;", "uiState", "getUiState", "clearSearchHistory", "", "clearSearchResults", "saveSearchHistory", "query", "searchFromHistory", "searchVideos", "toggleContentFilter", "updateSearchQuery", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class SearchViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.shipin.data.repository.VideoRepository videoRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.shipin.presentation.search.SearchUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.shipin.presentation.search.SearchUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<java.lang.String>> _searchHistory = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<java.lang.String>> searchHistory = null;
    
    @javax.inject.Inject()
    public SearchViewModel(@org.jetbrains.annotations.NotNull()
    com.example.shipin.data.repository.VideoRepository videoRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.shipin.presentation.search.SearchUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<java.lang.String>> getSearchHistory() {
        return null;
    }
    
    /**
     * 更新搜索关键词
     */
    public final void updateSearchQuery(@org.jetbrains.annotations.NotNull()
    java.lang.String query) {
    }
    
    /**
     * 执行搜索（学习LibreTV的search函数）
     */
    public final void searchVideos() {
    }
    
    /**
     * 切换内容过滤开关
     */
    public final void toggleContentFilter() {
    }
    
    /**
     * 清除搜索结果
     */
    public final void clearSearchResults() {
    }
    
    /**
     * 保存搜索历史（学习LibreTV的saveSearchHistory逻辑）
     */
    private final void saveSearchHistory(java.lang.String query) {
    }
    
    /**
     * 清除搜索历史
     */
    public final void clearSearchHistory() {
    }
    
    /**
     * 从历史记录搜索
     */
    public final void searchFromHistory(@org.jetbrains.annotations.NotNull()
    java.lang.String query) {
    }
}