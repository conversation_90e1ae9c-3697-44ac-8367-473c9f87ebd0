# 视频聚合App - 学习LibreTV技术实现

这是一个基于LibreTV技术的安卓视频聚合应用，学习并实现了LibreTV的核心功能。

## 🎯 项目特色

### 学习LibreTV的核心技术
- **多源聚合搜索** - 学习LibreTV的并发搜索和结果合并算法
- **M3U8广告过滤** - 移植LibreTV的DISCONTINUITY标记过滤技术
- **内容分类过滤** - 实现LibreTV的关键词过滤机制
- **搜索历史管理** - 学习LibreTV的本地存储设计

### 现代化技术栈
- **Jetpack Compose** - 现代化UI框架
- **MVVM + Clean Architecture** - 清晰的代码架构
- **Hilt依赖注入** - 模块化设计
- **Kotlin Coroutines + Flow** - 响应式编程
- **ExoPlayer** - 专业视频播放器

## 🏗️ 项目架构

```
app/
├── data/
│   ├── api/           # API接口和数据模型
│   └── repository/    # 数据仓库层
├── core/
│   └── player/        # 播放器核心（M3U8过滤）
├── presentation/
│   ├── search/        # 搜索界面
│   └── detail/        # 详情界面
└── di/                # 依赖注入模块
```

## 🔍 核心功能

### 1. 多源视频搜索
- 支持同时搜索多个视频API源
- 并发请求提高搜索速度
- 自动合并和展示搜索结果
- 显示视频来源信息

### 2. 内容过滤系统
- 成人内容关键词过滤
- 用户可控的过滤开关
- 基于LibreTV的过滤词库

### 3. 搜索历史管理
- 自动保存搜索历史
- 快速重复搜索
- 历史记录清理功能

### 4. 视频详情展示
- 完整的视频信息展示
- 剧集列表和选集播放
- 视频简介和演员信息

## 🛡️ 广告过滤技术

### M3U8广告过滤原理
学习LibreTV的核心过滤算法：

```kotlin
// 过滤DISCONTINUITY标记（广告插入点）
fun filterAds(m3u8Content: String): String {
    return m3u8Content.split("\n")
        .filter { !it.contains("#EXT-X-DISCONTINUITY") }
        .joinToString("\n")
}
```

### 过滤策略
1. **DISCONTINUITY标记过滤** - 移除广告插入点标记
2. **短片段过滤** - 过滤异常短的视频片段
3. **关键词过滤** - 识别包含广告关键词的URL

## 📱 使用说明

### 安装要求
- Android 7.0 (API 24) 及以上
- 网络连接

### 功能使用
1. **搜索视频** - 在搜索框输入关键词
2. **查看详情** - 点击搜索结果查看视频详情
3. **内容过滤** - 使用开关控制成人内容过滤
4. **搜索历史** - 快速访问历史搜索记录

## 🔧 开发配置

### 环境要求
- Android Studio Hedgehog | 2023.1.1+
- Kotlin 1.9.0+
- Gradle 8.0+

### 依赖库
- Jetpack Compose BOM 2024.09.00
- Hilt 2.48.1
- Retrofit 2.9.0
- ExoPlayer 1.2.1
- Room 2.6.1

### 构建步骤
```bash
# 克隆项目
git clone <repository-url>

# 打开Android Studio
# 导入项目并同步Gradle

# 运行项目
./gradlew assembleDebug
```

## 🎓 学习要点

### 从LibreTV学到的技术
1. **并发搜索设计** - 如何高效地聚合多个API源
2. **广告过滤算法** - M3U8流媒体广告过滤的实现
3. **内容过滤机制** - 基于关键词的内容分类过滤
4. **用户体验设计** - 搜索历史、加载状态等细节处理

### 技术改进
1. **现代化架构** - 使用MVVM和Clean Architecture
2. **类型安全** - Kotlin的类型安全和空安全
3. **响应式编程** - Flow和Coroutines的使用
4. **模块化设计** - Hilt依赖注入的模块化管理

## 📄 免责声明

本项目仅用于技术学习和研究目的：
- 学习LibreTV的技术实现
- 研究视频聚合和播放技术
- 不提供任何视频内容
- 请遵守相关法律法规

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 📜 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
