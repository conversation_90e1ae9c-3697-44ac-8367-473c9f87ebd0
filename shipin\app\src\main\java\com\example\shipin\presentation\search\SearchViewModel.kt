package com.example.shipin.presentation.search

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.shipin.data.api.ApiConfig
import com.example.shipin.data.api.VideoItem
import com.example.shipin.data.repository.SearchResult
import com.example.shipin.data.repository.VideoRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 搜索ViewModel - 学习LibreTV的搜索逻辑
 */
@HiltViewModel
class SearchViewModel @Inject constructor(
    private val videoRepository: VideoRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(SearchUiState())
    val uiState: StateFlow<SearchUiState> = _uiState.asStateFlow()
    
    private val _searchHistory = MutableStateFlow<List<String>>(emptyList())
    val searchHistory: StateFlow<List<String>> = _searchHistory.asStateFlow()
    
    /**
     * 更新搜索关键词
     */
    fun updateSearchQuery(query: String) {
        _uiState.value = _uiState.value.copy(searchQuery = query)
    }
    
    /**
     * 执行搜索（学习LibreTV的search函数）
     */
    fun searchVideos() {
        val query = _uiState.value.searchQuery.trim()
        if (query.isBlank()) return
        
        // 保存搜索历史
        saveSearchHistory(query)
        
        viewModelScope.launch {
            // 使用默认的API源进行搜索
            val enabledSources = ApiConfig.DEFAULT_SOURCES.filter { it.isEnabled }
            
            videoRepository.searchVideos(
                query = query,
                enabledSources = enabledSources,
                enableContentFilter = _uiState.value.contentFilterEnabled
            ).collect { result ->
                when (result) {
                    is SearchResult.Loading -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = true,
                            error = null
                        )
                    }
                    is SearchResult.Success -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            searchResults = result.videos,
                            error = null
                        )
                    }
                    is SearchResult.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = result.message
                        )
                    }
                }
            }
        }
    }
    
    /**
     * 切换内容过滤开关
     */
    fun toggleContentFilter() {
        _uiState.value = _uiState.value.copy(
            contentFilterEnabled = !_uiState.value.contentFilterEnabled
        )
    }
    
    /**
     * 清除搜索结果
     */
    fun clearSearchResults() {
        _uiState.value = _uiState.value.copy(
            searchResults = emptyList(),
            error = null
        )
    }
    
    /**
     * 保存搜索历史（学习LibreTV的saveSearchHistory逻辑）
     */
    private fun saveSearchHistory(query: String) {
        val currentHistory = _searchHistory.value.toMutableList()
        
        // 移除重复项
        currentHistory.remove(query)
        
        // 添加到开头
        currentHistory.add(0, query)
        
        // 限制历史记录数量
        if (currentHistory.size > 10) {
            currentHistory.removeAt(currentHistory.size - 1)
        }
        
        _searchHistory.value = currentHistory
    }
    
    /**
     * 清除搜索历史
     */
    fun clearSearchHistory() {
        _searchHistory.value = emptyList()
    }
    
    /**
     * 从历史记录搜索
     */
    fun searchFromHistory(query: String) {
        updateSearchQuery(query)
        searchVideos()
    }
}

/**
 * 搜索UI状态
 */
data class SearchUiState(
    val searchQuery: String = "",
    val isLoading: Boolean = false,
    val searchResults: List<VideoItem> = emptyList(),
    val error: String? = null,
    val contentFilterEnabled: Boolean = true
)
