package com.example.shipin.presentation.search;

import com.example.shipin.data.repository.VideoRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SearchViewModel_Factory implements Factory<SearchViewModel> {
  private final Provider<VideoRepository> videoRepositoryProvider;

  public SearchViewModel_Factory(Provider<VideoRepository> videoRepositoryProvider) {
    this.videoRepositoryProvider = videoRepositoryProvider;
  }

  @Override
  public SearchViewModel get() {
    return newInstance(videoRepositoryProvider.get());
  }

  public static SearchViewModel_Factory create(Provider<VideoRepository> videoRepositoryProvider) {
    return new SearchViewModel_Factory(videoRepositoryProvider);
  }

  public static SearchViewModel newInstance(VideoRepository videoRepository) {
    return new SearchViewModel(videoRepository);
  }
}
