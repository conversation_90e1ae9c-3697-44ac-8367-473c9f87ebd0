package com.example.shipin.data.api

import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Url

/**
 * 视频API服务接口 - 学习LibreTV的网络请求设计
 */
interface VideoApiService {
    
    /**
     * 通用GET请求 - 用于调用不同的视频源API
     */
    @GET
    suspend fun searchVideos(@Url url: String): Response<ApiResponse<VideoItem>>
    
    /**
     * 获取视频详情
     */
    @GET
    suspend fun getVideoDetail(@Url url: String): Response<ApiResponse<VideoItem>>
    
    /**
     * 获取M3U8内容（用于广告过滤）
     */
    @GET
    suspend fun getM3U8Content(@Url url: String): Response<String>
}
