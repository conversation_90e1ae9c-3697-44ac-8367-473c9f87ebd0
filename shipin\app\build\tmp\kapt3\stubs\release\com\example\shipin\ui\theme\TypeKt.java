package com.example.shipin.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\"\u0011\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Typography", "Landroidx/compose/material3/Typography;", "getTypography", "()Landroidx/compose/material3/Typography;", "app_release"})
public final class TypeKt {
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.material3.Typography Typography = null;
    
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.material3.Typography getTypography() {
        return null;
    }
}