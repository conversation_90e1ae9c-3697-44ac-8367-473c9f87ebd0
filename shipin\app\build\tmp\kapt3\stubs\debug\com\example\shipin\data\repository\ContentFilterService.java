package com.example.shipin.data.repository;

/**
 * 内容过滤服务（学习LibreTV的内容过滤逻辑）
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002J\u001a\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u00042\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a8\u0006\u0007"}, d2 = {"Lcom/example/shipin/data/repository/ContentFilterService;", "", "()V", "filterAdultContent", "", "Lcom/example/shipin/data/api/VideoItem;", "videos", "app_debug"})
public final class ContentFilterService {
    
    @javax.inject.Inject()
    public ContentFilterService() {
        super();
    }
    
    /**
     * 过滤成人内容
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.shipin.data.api.VideoItem> filterAdultContent(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.shipin.data.api.VideoItem> videos) {
        return null;
    }
}