package com.example.shipin

import com.example.shipin.core.player.M3U8AdFilter
import org.junit.Test
import org.junit.Assert.*

/**
 * M3U8广告过滤器测试 - 验证LibreTV的过滤算法
 */
class M3U8AdFilterTest {

    private val adFilter = M3U8AdFilter()

    @Test
    fun testFilterAds_removesDiscontinuity() {
        val input = """
            #EXTM3U
            #EXT-X-VERSION:3
            #EXT-X-TARGETDURATION:10
            #EXTINF:10.0,
            segment1.ts
            #EXT-X-DISCONTINUITY
            #EXTINF:5.0,
            ad_segment.ts
            #EXTINF:10.0,
            segment2.ts
            #EXT-X-ENDLIST
        """.trimIndent()

        val result = adFilter.filterAds(input)
        
        assertFalse("应该移除DISCONTINUITY标记", result.contains("#EXT-X-DISCONTINUITY"))
        assertTrue("应该保留正常片段", result.contains("segment1.ts"))
        assertTrue("应该保留正常片段", result.contains("segment2.ts"))
    }

    @Test
    fun testFilterAds_strictMode() {
        val input = """
            #EXTM3U
            #EXTINF:10.0,
            normal_segment.ts
            #EXTINF:1.5,
            short_ad.ts
            #EXTINF:10.0,
            another_segment.ts
        """.trimIndent()

        val result = adFilter.filterAds(input, strictMode = true)
        
        assertTrue("应该保留正常长度片段", result.contains("normal_segment.ts"))
        assertFalse("应该过滤短片段", result.contains("short_ad.ts"))
        assertTrue("应该保留正常长度片段", result.contains("another_segment.ts"))
    }

    @Test
    fun testProcessMasterPlaylist() {
        val baseUrl = "https://example.com/video"
        val input = """
            #EXTM3U
            #EXT-X-STREAM-INF:BANDWIDTH=1000000
            playlist1.m3u8
            #EXT-X-STREAM-INF:BANDWIDTH=2000000
            playlist2.m3u8
        """.trimIndent()

        val result = adFilter.processMasterPlaylist(input, baseUrl)
        
        assertTrue("应该包含流信息", result.contains("#EXT-X-STREAM-INF"))
        assertTrue("应该包含播放列表", result.contains("playlist1.m3u8"))
    }
}
