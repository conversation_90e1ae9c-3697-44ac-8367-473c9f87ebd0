1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.shipin"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- 网络权限 - 学习LibreTV的网络请求功能 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:7:22-76
14
15    <permission
15-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\3c0fd2c3401fe6d72a44db0beb141040\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
16        android:name="com.example.shipin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\3c0fd2c3401fe6d72a44db0beb141040\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\3c0fd2c3401fe6d72a44db0beb141040\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.example.shipin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\3c0fd2c3401fe6d72a44db0beb141040\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\3c0fd2c3401fe6d72a44db0beb141040\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:9:5-32:19
22        android:name="com.example.shipin.ShipinApplication"
22-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:10:9-42
23        android:allowBackup="true"
23-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:11:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\3c0fd2c3401fe6d72a44db0beb141040\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:12:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:13:9-54
29        android:icon="@mipmap/ic_launcher"
29-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:14:9-43
30        android:label="@string/app_name"
30-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:15:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:16:9-54
32        android:supportsRtl="true"
32-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:17:9-35
33        android:testOnly="true"
34        android:theme="@style/Theme.Shipin"
34-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:18:9-44
35        android:usesCleartextTraffic="true" >
35-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:19:9-44
36        <activity
36-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:21:9-31:20
37            android:name="com.example.shipin.MainActivity"
37-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:22:13-41
38            android:exported="true"
38-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:23:13-36
39            android:label="@string/app_name"
39-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:24:13-45
40            android:theme="@style/Theme.Shipin" >
40-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:25:13-48
41            <intent-filter>
41-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:26:13-30:29
42                <action android:name="android.intent.action.MAIN" />
42-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:27:17-69
42-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:27:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:29:17-77
44-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:29:27-74
45            </intent-filter>
46        </activity>
47        <activity
47-->[androidx.compose.ui:ui-tooling-android:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\560e8c69631f8d777312079a8799d77d\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
48            android:name="androidx.compose.ui.tooling.PreviewActivity"
48-->[androidx.compose.ui:ui-tooling-android:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\560e8c69631f8d777312079a8799d77d\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
49            android:exported="true" />
49-->[androidx.compose.ui:ui-tooling-android:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\560e8c69631f8d777312079a8799d77d\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
50
51        <provider
51-->[androidx.emoji2:emoji2:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\46423f38da5a6bdfd8435d5f486d4014\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
52            android:name="androidx.startup.InitializationProvider"
52-->[androidx.emoji2:emoji2:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\46423f38da5a6bdfd8435d5f486d4014\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
53            android:authorities="com.example.shipin.androidx-startup"
53-->[androidx.emoji2:emoji2:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\46423f38da5a6bdfd8435d5f486d4014\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
54            android:exported="false" >
54-->[androidx.emoji2:emoji2:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\46423f38da5a6bdfd8435d5f486d4014\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
55            <meta-data
55-->[androidx.emoji2:emoji2:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\46423f38da5a6bdfd8435d5f486d4014\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.emoji2.text.EmojiCompatInitializer"
56-->[androidx.emoji2:emoji2:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\46423f38da5a6bdfd8435d5f486d4014\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
57                android:value="androidx.startup" />
57-->[androidx.emoji2:emoji2:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\46423f38da5a6bdfd8435d5f486d4014\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
58            <meta-data
58-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\0582c02ad281a2ab4493fbbea758c3c1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
59-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\0582c02ad281a2ab4493fbbea758c3c1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
60                android:value="androidx.startup" />
60-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\0582c02ad281a2ab4493fbbea758c3c1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
62-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
63                android:value="androidx.startup" />
63-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
64        </provider>
65
66        <activity
66-->[androidx.compose.ui:ui-test-manifest:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\3ea77d0dcd0f97fe0971af7def9dae8d\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
67            android:name="androidx.activity.ComponentActivity"
67-->[androidx.compose.ui:ui-test-manifest:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\3ea77d0dcd0f97fe0971af7def9dae8d\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
68            android:exported="true" />
68-->[androidx.compose.ui:ui-test-manifest:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\3ea77d0dcd0f97fe0971af7def9dae8d\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
69
70        <service
70-->[androidx.room:room-runtime:2.6.1] D:\SDK\.gradle\caches\8.11.1\transforms\fd542a75e7b5197fc5971f94b8b87def\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
71            android:name="androidx.room.MultiInstanceInvalidationService"
71-->[androidx.room:room-runtime:2.6.1] D:\SDK\.gradle\caches\8.11.1\transforms\fd542a75e7b5197fc5971f94b8b87def\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
72            android:directBootAware="true"
72-->[androidx.room:room-runtime:2.6.1] D:\SDK\.gradle\caches\8.11.1\transforms\fd542a75e7b5197fc5971f94b8b87def\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
73            android:exported="false" />
73-->[androidx.room:room-runtime:2.6.1] D:\SDK\.gradle\caches\8.11.1\transforms\fd542a75e7b5197fc5971f94b8b87def\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
74
75        <receiver
75-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
76            android:name="androidx.profileinstaller.ProfileInstallReceiver"
76-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
77            android:directBootAware="false"
77-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
78            android:enabled="true"
78-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
79            android:exported="true"
79-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
80            android:permission="android.permission.DUMP" >
80-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
82                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
82-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
82-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
85                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
85-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
85-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
86            </intent-filter>
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
88                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
88-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
88-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
89            </intent-filter>
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
91                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
91-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
91-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
92            </intent-filter>
93        </receiver>
94    </application>
95
96</manifest>
