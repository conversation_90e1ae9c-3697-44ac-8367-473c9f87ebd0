package com.example.shipin.data.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ContentFilterService_Factory implements Factory<ContentFilterService> {
  @Override
  public ContentFilterService get() {
    return newInstance();
  }

  public static ContentFilterService_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static ContentFilterService newInstance() {
    return new ContentFilterService();
  }

  private static final class InstanceHolder {
    private static final ContentFilterService_Factory INSTANCE = new ContentFilterService_Factory();
  }
}
