package com.videohub.presentation.player

import android.content.Context
import androidx.media3.common.*
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.hls.HlsMediaSource
import androidx.media3.exoplayer.source.MediaSource
import androidx.media3.exoplayer.trackselection.DefaultTrackSelector
import com.videohub.core.player.FilteredDataSourceFactory
import com.videohub.core.player.M3U8AdFilter
import com.videohub.data.model.Episode
import kotlinx.coroutines.flow.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 视频播放器管理器 - 学习LibreTV的播放器实现
 */
@Singleton
class VideoPlayerManager @Inject constructor(
    private val context: Context,
    private val adFilter: M3U8AdFilter
) {
    
    private var _player: ExoPlayer? = null
    val player: ExoPlayer? get() = _player
    
    private val _playerState = MutableStateFlow(PlayerState.IDLE)
    val playerState: StateFlow<PlayerState> = _playerState.asStateFlow()
    
    private val _currentEpisode = MutableStateFlow<Episode?>(null)
    val currentEpisode: StateFlow<Episode?> = _currentEpisode.asStateFlow()
    
    private val _episodes = MutableStateFlow<List<Episode>>(emptyList())
    val episodes: StateFlow<List<Episode>> = _episodes.asStateFlow()
    
    private var _adFilteringEnabled = MutableStateFlow(true)
    val adFilteringEnabled: StateFlow<Boolean> = _adFilteringEnabled.asStateFlow()
    
    /**
     * 初始化播放器（学习LibreTV的initPlayer逻辑）
     */
    fun initializePlayer() {
        if (_player != null) return
        
        val trackSelector = DefaultTrackSelector(context).apply {
            setParameters(buildUponParameters().setMaxVideoSizeSd())
        }
        
        _player = ExoPlayer.Builder(context)
            .setTrackSelector(trackSelector)
            .build()
            .apply {
                addListener(playerListener)
            }
    }
    
    /**
     * 播放视频（学习LibreTV的playVideo逻辑）
     */
    fun playVideo(
        videoUrl: String,
        videoTitle: String,
        episodes: List<Episode> = emptyList(),
        startEpisodeIndex: Int = 0
    ) {
        _episodes.value = episodes
        
        if (episodes.isNotEmpty() && startEpisodeIndex < episodes.size) {
            _currentEpisode.value = episodes[startEpisodeIndex]
            playEpisode(startEpisodeIndex)
        } else {
            playDirectUrl(videoUrl, videoTitle)
        }
    }
    
    /**
     * 播放指定集数（学习LibreTV的playEpisode逻辑）
     */
    fun playEpisode(episodeIndex: Int) {
        val episodes = _episodes.value
        if (episodeIndex < 0 || episodeIndex >= episodes.size) return
        
        val episode = episodes[episodeIndex]
        _currentEpisode.value = episode
        
        playDirectUrl(episode.url, episode.name)
    }
    
    /**
     * 播放直接URL
     */
    private fun playDirectUrl(url: String, title: String) {
        val player = _player ?: return
        
        _playerState.value = PlayerState.LOADING
        
        try {
            val mediaSource = createMediaSource(url)
            player.setMediaSource(mediaSource)
            player.prepare()
            player.playWhenReady = true
            
        } catch (e: Exception) {
            _playerState.value = PlayerState.ERROR(e.message ?: "播放失败")
        }
    }
    
    /**
     * 创建媒体源（集成广告过滤）
     */
    private fun createMediaSource(url: String): MediaSource {
        val dataSourceFactory = FilteredDataSourceFactory(
            adFilter = adFilter,
            enableAdFiltering = { _adFilteringEnabled.value }
        )
        
        return if (url.contains(".m3u8")) {
            // HLS流媒体源
            HlsMediaSource.Factory(dataSourceFactory)
                .createMediaSource(MediaItem.fromUri(url))
        } else {
            // 其他格式的媒体源
            ProgressiveMediaSource.Factory(dataSourceFactory)
                .createMediaSource(MediaItem.fromUri(url))
        }
    }
    
    /**
     * 播放上一集（学习LibreTV的playPreviousEpisode逻辑）
     */
    fun playPreviousEpisode() {
        val episodes = _episodes.value
        val currentEpisode = _currentEpisode.value
        
        if (episodes.isEmpty() || currentEpisode == null) return
        
        val currentIndex = episodes.indexOf(currentEpisode)
        if (currentIndex > 0) {
            playEpisode(currentIndex - 1)
        }
    }
    
    /**
     * 播放下一集（学习LibreTV的playNextEpisode逻辑）
     */
    fun playNextEpisode() {
        val episodes = _episodes.value
        val currentEpisode = _currentEpisode.value
        
        if (episodes.isEmpty() || currentEpisode == null) return
        
        val currentIndex = episodes.indexOf(currentEpisode)
        if (currentIndex < episodes.size - 1) {
            playEpisode(currentIndex + 1)
        }
    }
    
    /**
     * 设置广告过滤开关（学习LibreTV的adFilteringEnabled逻辑）
     */
    fun setAdFilteringEnabled(enabled: Boolean) {
        _adFilteringEnabled.value = enabled
    }
    
    /**
     * 播放器事件监听器
     */
    private val playerListener = object : Player.Listener {
        override fun onPlaybackStateChanged(playbackState: Int) {
            _playerState.value = when (playbackState) {
                Player.STATE_IDLE -> PlayerState.IDLE
                Player.STATE_BUFFERING -> PlayerState.LOADING
                Player.STATE_READY -> PlayerState.READY
                Player.STATE_ENDED -> {
                    // 自动播放下一集（学习LibreTV的自动连播逻辑）
                    handleVideoEnded()
                    PlayerState.ENDED
                }
                else -> PlayerState.IDLE
            }
        }
        
        override fun onPlayerError(error: PlaybackException) {
            _playerState.value = PlayerState.ERROR(error.message ?: "播放错误")
        }
    }
    
    /**
     * 处理视频播放结束（自动连播逻辑）
     */
    private fun handleVideoEnded() {
        val episodes = _episodes.value
        val currentEpisode = _currentEpisode.value
        
        if (episodes.size > 1 && currentEpisode != null) {
            val currentIndex = episodes.indexOf(currentEpisode)
            if (currentIndex < episodes.size - 1) {
                // 3秒后自动播放下一集
                kotlinx.coroutines.GlobalScope.launch {
                    kotlinx.coroutines.delay(3000)
                    playNextEpisode()
                }
            }
        }
    }
    
    /**
     * 释放播放器资源
     */
    fun release() {
        _player?.release()
        _player = null
        _playerState.value = PlayerState.IDLE
    }
    
    /**
     * 暂停播放
     */
    fun pause() {
        _player?.pause()
    }
    
    /**
     * 恢复播放
     */
    fun resume() {
        _player?.play()
    }
    
    /**
     * 跳转到指定位置
     */
    fun seekTo(positionMs: Long) {
        _player?.seekTo(positionMs)
    }
    
    /**
     * 获取当前播放位置
     */
    fun getCurrentPosition(): Long {
        return _player?.currentPosition ?: 0L
    }
    
    /**
     * 获取视频总时长
     */
    fun getDuration(): Long {
        return _player?.duration ?: 0L
    }
}

/**
 * 播放器状态密封类
 */
sealed class PlayerState {
    object IDLE : PlayerState()
    object LOADING : PlayerState()
    object READY : PlayerState()
    object ENDED : PlayerState()
    data class ERROR(val message: String) : PlayerState()
}
