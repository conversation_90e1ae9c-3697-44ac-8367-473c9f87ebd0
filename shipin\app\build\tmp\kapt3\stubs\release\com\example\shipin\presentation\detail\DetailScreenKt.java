package com.example.shipin.presentation.detail;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000>\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u001aD\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u00062\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0007\u001a\u001e\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u00072\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0003\u001a*\u0010\u000f\u001a\u00020\u00012\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00070\u00112\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0006H\u0003\u001a,\u0010\u0013\u001a\u00020\u00012\u0006\u0010\u0014\u001a\u00020\u00032\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0003\u001a\u0010\u0010\u0016\u001a\u00020\u00012\u0006\u0010\u0017\u001a\u00020\u0003H\u0003\u001a$\u0010\u0018\u001a\u00020\u00012\u0006\u0010\u0019\u001a\u00020\u001a2\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0006H\u0003\u001a\u0010\u0010\u001b\u001a\u00020\u00012\u0006\u0010\u001c\u001a\u00020\u001dH\u0003\u00a8\u0006\u001e"}, d2 = {"DetailScreen", "", "videoId", "", "sourceCode", "onPlayClick", "Lkotlin/Function1;", "Lcom/example/shipin/data/api/Episode;", "onBackClick", "Lkotlin/Function0;", "viewModel", "Lcom/example/shipin/presentation/detail/DetailViewModel;", "EpisodeChip", "episode", "onClick", "EpisodesSection", "episodes", "", "onEpisodeClick", "ErrorMessage", "message", "onRetry", "VideoDescriptionSection", "content", "VideoDetailContent", "videoDetail", "Lcom/example/shipin/data/api/VideoDetail;", "VideoInfoSection", "videoInfo", "Lcom/example/shipin/data/api/VideoItem;", "app_release"})
public final class DetailScreenKt {
    
    /**
     * 视频详情界面 - 学习LibreTV的详情页设计
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void DetailScreen(@org.jetbrains.annotations.NotNull()
    java.lang.String videoId, @org.jetbrains.annotations.NotNull()
    java.lang.String sourceCode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.shipin.data.api.Episode, kotlin.Unit> onPlayClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick, @org.jetbrains.annotations.NotNull()
    com.example.shipin.presentation.detail.DetailViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void VideoDetailContent(com.example.shipin.data.api.VideoDetail videoDetail, kotlin.jvm.functions.Function1<? super com.example.shipin.data.api.Episode, kotlin.Unit> onPlayClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void VideoInfoSection(com.example.shipin.data.api.VideoItem videoInfo) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void EpisodesSection(java.util.List<com.example.shipin.data.api.Episode> episodes, kotlin.jvm.functions.Function1<? super com.example.shipin.data.api.Episode, kotlin.Unit> onEpisodeClick) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void EpisodeChip(com.example.shipin.data.api.Episode episode, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void VideoDescriptionSection(java.lang.String content) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ErrorMessage(java.lang.String message, kotlin.jvm.functions.Function0<kotlin.Unit> onRetry, kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick) {
    }
}