package com.videohub.core.player

import okhttp3.*
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * M3U8广告过滤器 - 学习LibreTV的filterAdsFromM3U8逻辑
 */
@Singleton
class M3U8AdFilter @Inject constructor() {
    
    /**
     * 过滤M3U8内容中的广告（学习LibreTV的核心过滤算法）
     */
    fun filterAds(m3u8Content: String, strictMode: Boolean = false): String {
        if (m3u8Content.isBlank()) return ""
        
        val lines = m3u8Content.split("\n")
        val filteredLines = mutableListOf<String>()
        
        var i = 0
        while (i < lines.size) {
            val line = lines[i].trim()
            
            // 1. 过滤DISCONTINUITY标记（LibreTV的主要过滤逻辑）
            if (line.contains("#EXT-X-DISCONTINUITY")) {
                // 跳过DISCONTINUITY标记
                i++
                continue
            }
            
            // 2. 严格模式下的额外过滤
            if (strictMode) {
                // 过滤异常短的片段（可能是广告）
                if (line.startsWith("#EXTINF:")) {
                    val duration = extractDuration(line)
                    if (duration in 0.1..2.0) {
                        // 跳过当前EXTINF行和下一行的URL
                        i += 2
                        continue
                    }
                }
                
                // 过滤包含广告关键词的URL
                if (!line.startsWith("#") && containsAdKeywords(line)) {
                    i++
                    continue
                }
            }
            
            filteredLines.add(line)
            i++
        }
        
        return filteredLines.joinToString("\n")
    }
    
    /**
     * 提取EXTINF行中的时长
     */
    private fun extractDuration(extinfLine: String): Double {
        return try {
            val regex = Regex("#EXTINF:([\\d.]+),")
            val matchResult = regex.find(extinfLine)
            matchResult?.groupValues?.get(1)?.toDouble() ?: 0.0
        } catch (e: Exception) {
            0.0
        }
    }
    
    /**
     * 检查URL是否包含广告关键词
     */
    private fun containsAdKeywords(url: String): Boolean {
        val adKeywords = listOf("ad", "ads", "advertisement", "advert", "commercial")
        val lowerUrl = url.lowercase()
        return adKeywords.any { keyword -> lowerUrl.contains(keyword) }
    }
    
    /**
     * 处理主播放列表（Master Playlist）
     */
    fun processMasterPlaylist(content: String, baseUrl: String): String {
        val lines = content.split("\n")
        val processedLines = mutableListOf<String>()
        
        lines.forEach { line ->
            when {
                line.startsWith("#EXT-X-STREAM-INF") -> {
                    processedLines.add(line)
                }
                line.startsWith("http") || (!line.startsWith("#") && line.isNotBlank()) -> {
                    // 将相对URL转换为通过我们的过滤器的URL
                    val absoluteUrl = resolveUrl(baseUrl, line)
                    processedLines.add(absoluteUrl)
                }
                else -> {
                    processedLines.add(line)
                }
            }
        }
        
        return processedLines.joinToString("\n")
    }
    
    /**
     * 解析相对URL为绝对URL
     */
    private fun resolveUrl(baseUrl: String, relativeUrl: String): String {
        return if (relativeUrl.startsWith("http")) {
            relativeUrl
        } else {
            val base = if (baseUrl.endsWith("/")) baseUrl.dropLast(1) else baseUrl
            val relative = if (relativeUrl.startsWith("/")) relativeUrl else "/$relativeUrl"
            "$base$relative"
        }
    }
}

/**
 * 自定义OkHttp拦截器 - 实现M3U8内容过滤
 */
class M3U8FilterInterceptor(
    private val adFilter: M3U8AdFilter,
    private val enableAdFiltering: () -> Boolean
) : Interceptor {
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(request)
        
        // 只处理M3U8文件
        if (!isM3U8Request(request, response)) {
            return response
        }
        
        // 检查是否启用广告过滤
        if (!enableAdFiltering()) {
            return response
        }
        
        return try {
            val originalContent = response.body?.string() ?: ""
            val filteredContent = adFilter.filterAds(originalContent, strictMode = true)
            
            // 创建新的响应体
            val newBody = ResponseBody.create(
                response.body?.contentType(),
                filteredContent
            )
            
            response.newBuilder()
                .body(newBody)
                .build()
                
        } catch (e: Exception) {
            // 过滤失败时返回原始响应
            response
        }
    }
    
    /**
     * 判断是否为M3U8请求
     */
    private fun isM3U8Request(request: Request, response: Response): Boolean {
        val url = request.url.toString()
        val contentType = response.header("Content-Type") ?: ""
        
        return url.contains(".m3u8") || 
               contentType.contains("application/vnd.apple.mpegurl") ||
               contentType.contains("application/x-mpegURL")
    }
}

/**
 * ExoPlayer数据源工厂 - 集成广告过滤
 */
class FilteredDataSourceFactory(
    private val adFilter: M3U8AdFilter,
    private val enableAdFiltering: () -> Boolean
) : DataSource.Factory {
    
    private val httpDataSourceFactory = DefaultHttpDataSource.Factory()
        .setUserAgent("VideoHub-Android/1.0")
        .setConnectTimeoutMs(30000)
        .setReadTimeoutMs(30000)
    
    override fun createDataSource(): DataSource {
        val httpDataSource = httpDataSourceFactory.createDataSource()
        
        // 如果启用广告过滤，包装数据源
        return if (enableAdFiltering()) {
            FilteredDataSource(httpDataSource, adFilter)
        } else {
            httpDataSource
        }
    }
}

/**
 * 自定义数据源 - 在数据读取时进行过滤
 */
class FilteredDataSource(
    private val upstream: DataSource,
    private val adFilter: M3U8AdFilter
) : DataSource {
    
    override fun open(dataSpec: DataSpec): Long {
        return upstream.open(dataSpec)
    }
    
    override fun read(buffer: ByteArray, offset: Int, readLength: Int): Int {
        val bytesRead = upstream.read(buffer, offset, readLength)
        
        // 如果是M3U8内容，进行过滤
        if (bytesRead > 0 && isM3U8Content(dataSpec.uri.toString())) {
            val content = String(buffer, offset, bytesRead)
            val filteredContent = adFilter.filterAds(content)
            val filteredBytes = filteredContent.toByteArray()
            
            if (filteredBytes.size <= buffer.size - offset) {
                System.arraycopy(filteredBytes, 0, buffer, offset, filteredBytes.size)
                return filteredBytes.size
            }
        }
        
        return bytesRead
    }
    
    override fun getUri(): Uri? = upstream.uri
    
    override fun close() = upstream.close()
    
    override fun addTransferListener(transferListener: TransferListener) {
        upstream.addTransferListener(transferListener)
    }
    
    private fun isM3U8Content(url: String): Boolean {
        return url.contains(".m3u8")
    }
}
