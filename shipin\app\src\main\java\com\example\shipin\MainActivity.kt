package com.example.shipin

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.shipin.data.api.VideoItem
import com.example.shipin.presentation.search.SearchScreen
import com.example.shipin.presentation.detail.DetailScreen
import com.example.shipin.ui.theme.ShipinTheme
import dagger.hilt.android.AndroidEntryPoint

/**
 * 主Activity - 学习LibreTV的界面设计
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            ShipinTheme {
                ShipinApp()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ShipinApp() {
    val navController = rememberNavController()

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            TopAppBar(
                title = { Text("视频聚合") }
            )
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = "search",
            modifier = Modifier.padding(innerPadding)
        ) {
            composable("search") {
                SearchScreen(
                    onVideoClick = { video ->
                        // 导航到视频详情页面
                        navController.navigate("detail/${video.vod_id}/${video.source_code}")
                    }
                )
            }

            composable("detail/{videoId}/{sourceCode}") { backStackEntry ->
                val videoId = backStackEntry.arguments?.getString("videoId") ?: ""
                val sourceCode = backStackEntry.arguments?.getString("sourceCode") ?: ""

                DetailScreen(
                    videoId = videoId,
                    sourceCode = sourceCode,
                    onPlayClick = { episode ->
                        // TODO: 导航到播放器页面
                        // navController.navigate("player/${Uri.encode(episode.url)}")
                    },
                    onBackClick = {
                        navController.popBackStack()
                    }
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ShipinAppPreview() {
    ShipinTheme {
        ShipinApp()
    }
}