package com.example.shipin.presentation.search;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000F\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a\u001e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001aR\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\u00032\u0012\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0006\u0010\u000b\u001a\u00020\f2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u000e\u001a\u00020\u000fH\u0003\u001a8\u0010\u0010\u001a\u00020\u00012\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00030\u00122\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\t2\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a*\u0010\u0015\u001a\u00020\u00012\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00170\u00122\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0017\u0012\u0004\u0012\u00020\u00010\tH\u0003\u001a&\u0010\u0019\u001a\u00020\u00012\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0017\u0012\u0004\u0012\u00020\u00010\t2\b\b\u0002\u0010\u001a\u001a\u00020\u001bH\u0007\u001a(\u0010\u001c\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\u00172\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u000e\u001a\u00020\u000fH\u0003\u00a8\u0006\u001f"}, d2 = {"ErrorMessage", "", "message", "", "onRetry", "Lkotlin/Function0;", "SearchBar", "query", "onQueryChange", "Lkotlin/Function1;", "onSearch", "contentFilterEnabled", "", "onToggleContentFilter", "modifier", "Landroidx/compose/ui/Modifier;", "SearchHistorySection", "history", "", "onHistoryClick", "onClearHistory", "SearchResults", "videos", "Lcom/example/shipin/data/api/VideoItem;", "onVideoClick", "SearchScreen", "viewModel", "Lcom/example/shipin/presentation/search/SearchViewModel;", "VideoCard", "video", "onClick", "app_release"})
public final class SearchScreenKt {
    
    /**
     * 搜索界面 - 学习LibreTV的搜索UI设计
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SearchScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.shipin.data.api.VideoItem, kotlin.Unit> onVideoClick, @org.jetbrains.annotations.NotNull()
    com.example.shipin.presentation.search.SearchViewModel viewModel) {
    }
    
    /**
     * 搜索栏组件
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void SearchBar(java.lang.String query, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onQueryChange, kotlin.jvm.functions.Function0<kotlin.Unit> onSearch, boolean contentFilterEnabled, kotlin.jvm.functions.Function0<kotlin.Unit> onToggleContentFilter, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 搜索历史组件
     */
    @androidx.compose.runtime.Composable()
    private static final void SearchHistorySection(java.util.List<java.lang.String> history, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onHistoryClick, kotlin.jvm.functions.Function0<kotlin.Unit> onClearHistory) {
    }
    
    /**
     * 搜索结果列表
     */
    @androidx.compose.runtime.Composable()
    private static final void SearchResults(java.util.List<com.example.shipin.data.api.VideoItem> videos, kotlin.jvm.functions.Function1<? super com.example.shipin.data.api.VideoItem, kotlin.Unit> onVideoClick) {
    }
    
    /**
     * 视频卡片组件（学习LibreTV的视频卡片设计）
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void VideoCard(com.example.shipin.data.api.VideoItem video, kotlin.jvm.functions.Function0<kotlin.Unit> onClick, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 错误信息组件
     */
    @androidx.compose.runtime.Composable()
    private static final void ErrorMessage(java.lang.String message, kotlin.jvm.functions.Function0<kotlin.Unit> onRetry) {
    }
}