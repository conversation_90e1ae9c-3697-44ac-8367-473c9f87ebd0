package com.example.shipin.presentation.detail

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.shipin.data.api.VideoDetail
import com.example.shipin.data.repository.VideoRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 详情页ViewModel - 学习LibreTV的详情页逻辑
 */
@HiltViewModel
class DetailViewModel @Inject constructor(
    private val videoRepository: VideoRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(DetailUiState())
    val uiState: StateFlow<DetailUiState> = _uiState.asStateFlow()
    
    /**
     * 加载视频详情
     */
    fun loadVideoDetail(videoId: String, sourceCode: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                error = null
            )
            
            videoRepository.getVideoDetail(videoId, sourceCode)
                .onSuccess { videoDetail ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        videoDetail = videoDetail,
                        error = null
                    )
                }
                .onFailure { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "加载失败"
                    )
                }
        }
    }
}

/**
 * 详情页UI状态
 */
data class DetailUiState(
    val isLoading: Boolean = false,
    val videoDetail: VideoDetail? = null,
    val error: String? = null
)
