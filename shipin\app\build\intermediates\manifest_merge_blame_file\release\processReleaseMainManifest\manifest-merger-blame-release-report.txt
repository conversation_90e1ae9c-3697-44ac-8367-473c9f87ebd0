1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.shipin"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- 网络权限 - 学习LibreTV的网络请求功能 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:7:22-76
14
15    <permission
15-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\3c0fd2c3401fe6d72a44db0beb141040\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
16        android:name="com.example.shipin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\3c0fd2c3401fe6d72a44db0beb141040\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\3c0fd2c3401fe6d72a44db0beb141040\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.example.shipin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\3c0fd2c3401fe6d72a44db0beb141040\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\3c0fd2c3401fe6d72a44db0beb141040\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:9:5-32:19
22        android:name="com.example.shipin.ShipinApplication"
22-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:10:9-42
23        android:allowBackup="true"
23-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:11:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\3c0fd2c3401fe6d72a44db0beb141040\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:12:9-65
26        android:extractNativeLibs="false"
27        android:fullBackupContent="@xml/backup_rules"
27-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:13:9-54
28        android:icon="@mipmap/ic_launcher"
28-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:14:9-43
29        android:label="@string/app_name"
29-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:15:9-41
30        android:roundIcon="@mipmap/ic_launcher_round"
30-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:16:9-54
31        android:supportsRtl="true"
31-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:17:9-35
32        android:theme="@style/Theme.Shipin"
32-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:18:9-44
33        android:usesCleartextTraffic="true" >
33-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:19:9-44
34        <activity
34-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:21:9-31:20
35            android:name="com.example.shipin.MainActivity"
35-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:22:13-41
36            android:exported="true"
36-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:23:13-36
37            android:label="@string/app_name"
37-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:24:13-45
38            android:theme="@style/Theme.Shipin" >
38-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:25:13-48
39            <intent-filter>
39-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:26:13-30:29
40                <action android:name="android.intent.action.MAIN" />
40-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:27:17-69
40-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:27:25-66
41
42                <category android:name="android.intent.category.LAUNCHER" />
42-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:29:17-77
42-->D:\csxm\LibreTV\shipin\app\src\main\AndroidManifest.xml:29:27-74
43            </intent-filter>
44        </activity>
45
46        <provider
46-->[androidx.emoji2:emoji2:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\46423f38da5a6bdfd8435d5f486d4014\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
47            android:name="androidx.startup.InitializationProvider"
47-->[androidx.emoji2:emoji2:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\46423f38da5a6bdfd8435d5f486d4014\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
48            android:authorities="com.example.shipin.androidx-startup"
48-->[androidx.emoji2:emoji2:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\46423f38da5a6bdfd8435d5f486d4014\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
49            android:exported="false" >
49-->[androidx.emoji2:emoji2:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\46423f38da5a6bdfd8435d5f486d4014\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
50            <meta-data
50-->[androidx.emoji2:emoji2:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\46423f38da5a6bdfd8435d5f486d4014\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
51                android:name="androidx.emoji2.text.EmojiCompatInitializer"
51-->[androidx.emoji2:emoji2:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\46423f38da5a6bdfd8435d5f486d4014\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
52                android:value="androidx.startup" />
52-->[androidx.emoji2:emoji2:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\46423f38da5a6bdfd8435d5f486d4014\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
53            <meta-data
53-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\0582c02ad281a2ab4493fbbea758c3c1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
54                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
54-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\0582c02ad281a2ab4493fbbea758c3c1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
55                android:value="androidx.startup" />
55-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\0582c02ad281a2ab4493fbbea758c3c1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
56            <meta-data
56-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
57                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
57-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
58                android:value="androidx.startup" />
58-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
59        </provider>
60
61        <service
61-->[androidx.room:room-runtime:2.6.1] D:\SDK\.gradle\caches\8.11.1\transforms\fd542a75e7b5197fc5971f94b8b87def\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
62            android:name="androidx.room.MultiInstanceInvalidationService"
62-->[androidx.room:room-runtime:2.6.1] D:\SDK\.gradle\caches\8.11.1\transforms\fd542a75e7b5197fc5971f94b8b87def\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
63            android:directBootAware="true"
63-->[androidx.room:room-runtime:2.6.1] D:\SDK\.gradle\caches\8.11.1\transforms\fd542a75e7b5197fc5971f94b8b87def\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
64            android:exported="false" />
64-->[androidx.room:room-runtime:2.6.1] D:\SDK\.gradle\caches\8.11.1\transforms\fd542a75e7b5197fc5971f94b8b87def\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
65
66        <receiver
66-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
67            android:name="androidx.profileinstaller.ProfileInstallReceiver"
67-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
68            android:directBootAware="false"
68-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
69            android:enabled="true"
69-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
70            android:exported="true"
70-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
71            android:permission="android.permission.DUMP" >
71-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
72            <intent-filter>
72-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
73                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
73-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
73-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
74            </intent-filter>
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
76                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
76-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
76-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
77            </intent-filter>
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
79                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
79-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
79-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
82                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
82-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
82-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\bda6b8f5cc63b1a055bcfd7176027fca\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
83            </intent-filter>
84        </receiver>
85    </application>
86
87</manifest>
