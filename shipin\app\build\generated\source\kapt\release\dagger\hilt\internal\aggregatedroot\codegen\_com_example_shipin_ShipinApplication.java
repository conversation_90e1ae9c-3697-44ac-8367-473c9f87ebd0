package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.example.shipin.ShipinApplication",
    rootPackage = "com.example.shipin",
    originatingRoot = "com.example.shipin.ShipinApplication",
    originatingRootPackage = "com.example.shipin",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "ShipinApplication",
    originatingRootSimpleNames = "ShipinApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_example_shipin_ShipinApplication {
}
