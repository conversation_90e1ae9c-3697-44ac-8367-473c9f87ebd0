package com.example.shipin.presentation.detail;

import com.example.shipin.data.repository.VideoRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DetailViewModel_Factory implements Factory<DetailViewModel> {
  private final Provider<VideoRepository> videoRepositoryProvider;

  public DetailViewModel_Factory(Provider<VideoRepository> videoRepositoryProvider) {
    this.videoRepositoryProvider = videoRepositoryProvider;
  }

  @Override
  public DetailViewModel get() {
    return newInstance(videoRepositoryProvider.get());
  }

  public static DetailViewModel_Factory create(Provider<VideoRepository> videoRepositoryProvider) {
    return new DetailViewModel_Factory(videoRepositoryProvider);
  }

  public static DetailViewModel newInstance(VideoRepository videoRepository) {
    return new DetailViewModel(videoRepository);
  }
}
