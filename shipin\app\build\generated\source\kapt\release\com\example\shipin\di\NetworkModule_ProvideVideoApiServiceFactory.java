package com.example.shipin.di;

import com.example.shipin.data.api.VideoApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideVideoApiServiceFactory implements Factory<VideoApiService> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvideVideoApiServiceFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public VideoApiService get() {
    return provideVideoApiService(retrofitProvider.get());
  }

  public static NetworkModule_ProvideVideoApiServiceFactory create(
      Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvideVideoApiServiceFactory(retrofitProvider);
  }

  public static VideoApiService provideVideoApiService(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideVideoApiService(retrofit));
  }
}
