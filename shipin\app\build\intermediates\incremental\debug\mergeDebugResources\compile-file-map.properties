#Wed Jun 04 14:14:24 CST 2025
com.example.shipin.app-main-49\:/drawable/ic_launcher_background.xml=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.shipin.app-main-49\:/drawable/ic_launcher_foreground.xml=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.example.shipin.app-main-49\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.shipin.app-main-49\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.shipin.app-main-49\:/mipmap-hdpi/ic_launcher.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.shipin.app-main-49\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.shipin.app-main-49\:/mipmap-mdpi/ic_launcher.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.shipin.app-main-49\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.shipin.app-main-49\:/mipmap-xhdpi/ic_launcher.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.shipin.app-main-49\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.shipin.app-main-49\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.shipin.app-main-49\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.shipin.app-main-49\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.shipin.app-main-49\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.shipin.app-main-49\:/xml/backup_rules.xml=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.shipin.app-main-49\:/xml/data_extraction_rules.xml=D\:\\csxm\\LibreTV\\shipin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
