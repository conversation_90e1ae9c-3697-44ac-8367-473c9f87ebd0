package com.example.shipin.data.api;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0005\b\u00c7\u0002\u0018\u00002\u00020\u0001:\u0002\b\tB\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0017\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\n"}, d2 = {"Lcom/example/shipin/data/api/ApiConfig;", "", "()V", "DEFAULT_SOURCES", "", "Lcom/example/shipin/data/api/ApiSource;", "getDEFAULT_SOURCES", "()Ljava/util/List;", "ContentFilter", "RequestConfig", "app_debug"})
public final class ApiConfig {
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<com.example.shipin.data.api.ApiSource> DEFAULT_SOURCES = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.shipin.data.api.ApiConfig INSTANCE = null;
    
    private ApiConfig() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.shipin.data.api.ApiSource> getDEFAULT_SOURCES() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0017\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\b"}, d2 = {"Lcom/example/shipin/data/api/ApiConfig$ContentFilter;", "", "()V", "ADULT_KEYWORDS", "", "", "getADULT_KEYWORDS", "()Ljava/util/List;", "app_debug"})
    public static final class ContentFilter {
        @org.jetbrains.annotations.NotNull()
        private static final java.util.List<java.lang.String> ADULT_KEYWORDS = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.example.shipin.data.api.ApiConfig.ContentFilter INSTANCE = null;
        
        private ContentFilter() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> getADULT_KEYWORDS() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\t\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u001d\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u000e\u0010\b\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/example/shipin/data/api/ApiConfig$RequestConfig;", "", "()V", "DEFAULT_HEADERS", "", "", "getDEFAULT_HEADERS", "()Ljava/util/Map;", "DETAIL_PATH", "MAX_CONCURRENT_REQUESTS", "", "MAX_PAGES", "PAGE_PATH", "SEARCH_PATH", "TIMEOUT_SECONDS", "", "app_debug"})
    public static final class RequestConfig {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String SEARCH_PATH = "?ac=videolist&wd=";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String DETAIL_PATH = "?ac=videolist&ids=";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String PAGE_PATH = "?ac=videolist&wd={query}&pg={page}";
        public static final int MAX_PAGES = 3;
        public static final long TIMEOUT_SECONDS = 8L;
        public static final int MAX_CONCURRENT_REQUESTS = 5;
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Map<java.lang.String, java.lang.String> DEFAULT_HEADERS = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.example.shipin.data.api.ApiConfig.RequestConfig INSTANCE = null;
        
        private RequestConfig() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Map<java.lang.String, java.lang.String> getDEFAULT_HEADERS() {
            return null;
        }
    }
}