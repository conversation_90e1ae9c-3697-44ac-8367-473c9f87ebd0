package com.example.shipin.core.player;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class M3U8AdFilter_Factory implements Factory<M3U8AdFilter> {
  @Override
  public M3U8AdFilter get() {
    return newInstance();
  }

  public static M3U8AdFilter_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static M3U8AdFilter newInstance() {
    return new M3U8AdFilter();
  }

  private static final class InstanceHolder {
    private static final M3U8AdFilter_Factory INSTANCE = new M3U8AdFilter_Factory();
  }
}
