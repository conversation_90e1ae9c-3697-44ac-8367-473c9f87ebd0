package com.example.shipin.presentation.detail

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import com.example.shipin.data.api.Episode
import com.example.shipin.data.api.VideoDetail

/**
 * 视频详情界面 - 学习LibreTV的详情页设计
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DetailScreen(
    videoId: String,
    sourceCode: String,
    onPlayClick: (Episode) -> Unit,
    onBackClick: () -> Unit,
    viewModel: DetailViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    LaunchedEffect(videoId, sourceCode) {
        viewModel.loadVideoDetail(videoId, sourceCode)
    }
    
    when {
        uiState.isLoading -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        }
        
        uiState.error != null -> {
            val errorMessage = uiState.error
            ErrorMessage(
                message = errorMessage,
                onRetry = { viewModel.loadVideoDetail(videoId, sourceCode) },
                onBackClick = onBackClick
            )
        }

        uiState.videoDetail != null -> {
            val videoDetail = uiState.videoDetail
            VideoDetailContent(
                videoDetail = videoDetail,
                onPlayClick = onPlayClick
            )
        }
    }
}

@Composable
private fun VideoDetailContent(
    videoDetail: VideoDetail,
    onPlayClick: (Episode) -> Unit
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // 视频基本信息
            VideoInfoSection(videoDetail.videoInfo)
        }
        
        item {
            // 剧集列表
            if (videoDetail.episodes.isNotEmpty()) {
                EpisodesSection(
                    episodes = videoDetail.episodes,
                    onEpisodeClick = onPlayClick
                )
            }
        }
        
        item {
            // 视频简介
            videoDetail.videoInfo.vod_content?.let { content ->
                VideoDescriptionSection(content)
            }
        }
    }
}

@Composable
private fun VideoInfoSection(videoInfo: com.example.shipin.data.api.VideoItem) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.padding(16.dp)
        ) {
            // 封面图片
            AsyncImage(
                model = videoInfo.vod_pic,
                contentDescription = videoInfo.vod_name,
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .size(120.dp, 160.dp)
                    .padding(end = 16.dp)
            )
            
            // 视频信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = videoInfo.vod_name,
                    style = MaterialTheme.typography.headlineSmall,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 标签信息
                Row {
                    videoInfo.vod_year?.let { year ->
                        AssistChip(
                            onClick = { },
                            label = { Text(year) },
                            modifier = Modifier.padding(end = 4.dp)
                        )
                    }
                    
                    videoInfo.vod_area?.let { area ->
                        AssistChip(
                            onClick = { },
                            label = { Text(area) }
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 导演和演员
                videoInfo.vod_director?.let { director ->
                    Text(
                        text = "导演: $director",
                        style = MaterialTheme.typography.bodyMedium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                videoInfo.vod_actor?.let { actor ->
                    Text(
                        text = "演员: $actor",
                        style = MaterialTheme.typography.bodyMedium,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 来源信息
                videoInfo.source_name?.let { source ->
                    Text(
                        text = "来源: $source",
                        style = MaterialTheme.typography.labelMedium,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}

@Composable
private fun EpisodesSection(
    episodes: List<Episode>,
    onEpisodeClick: (Episode) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "选集播放",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(episodes) { episode ->
                    EpisodeChip(
                        episode = episode,
                        onClick = { onEpisodeClick(episode) }
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EpisodeChip(
    episode: Episode,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                Icons.Default.PlayArrow,
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = episode.name,
                style = MaterialTheme.typography.labelMedium
            )
        }
    }
}

@Composable
private fun VideoDescriptionSection(content: String) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "剧情简介",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = content,
                style = MaterialTheme.typography.bodyMedium,
                lineHeight = MaterialTheme.typography.bodyMedium.lineHeight
            )
        }
    }
}

@Composable
private fun ErrorMessage(
    message: String,
    onRetry: () -> Unit,
    onBackClick: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = message,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Row {
            OutlinedButton(onClick = onBackClick) {
                Text("返回")
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Button(onClick = onRetry) {
                Text("重试")
            }
        }
    }
}
