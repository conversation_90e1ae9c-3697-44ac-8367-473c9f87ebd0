package com.example.shipin.data.repository;

import com.example.shipin.data.api.VideoApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class VideoRepository_Factory implements Factory<VideoRepository> {
  private final Provider<VideoApiService> apiServiceProvider;

  private final Provider<ContentFilterService> contentFilterProvider;

  public VideoRepository_Factory(Provider<VideoApiService> apiServiceProvider,
      Provider<ContentFilterService> contentFilterProvider) {
    this.apiServiceProvider = apiServiceProvider;
    this.contentFilterProvider = contentFilterProvider;
  }

  @Override
  public VideoRepository get() {
    return newInstance(apiServiceProvider.get(), contentFilterProvider.get());
  }

  public static VideoRepository_Factory create(Provider<VideoApiService> apiServiceProvider,
      Provider<ContentFilterService> contentFilterProvider) {
    return new VideoRepository_Factory(apiServiceProvider, contentFilterProvider);
  }

  public static VideoRepository newInstance(VideoApiService apiService,
      ContentFilterService contentFilter) {
    return new VideoRepository(apiService, contentFilter);
  }
}
